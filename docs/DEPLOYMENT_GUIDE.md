# VTravels Deployment Guide

## Overview

This guide covers deploying the VTravels application in different environments using Docker and Docker Compose.

## Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Git
- 4GB+ RAM
- 20GB+ disk space

## Quick Start (Development)

1. **Clone the repository:**
```bash
git clone <repository-url>
cd vtravels
```

2. **Start all services:**
```bash
docker-compose -f docker-compose.dev.yml up -d
```

3. **Access the application:**
- Frontend: http://localhost:3000
- API Gateway: http://localhost:8080
- Python Backend: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Production Deployment

### Environment Variables

Create a `.env` file in the project root:

```env
# Database Configuration
MYSQL_ROOT_PASSWORD=your-secure-root-password
MYSQL_PASSWORD=your-secure-password
MONGO_INITDB_ROOT_PASSWORD=your-mongo-password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# External APIs
STRIPE_SECRET_KEY=sk_live_...
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret

# Domain Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### Production Deployment Steps

1. **Prepare the server:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

2. **Clone and configure:**
```bash
git clone <repository-url>
cd vtravels
cp .env.example .env
# Edit .env with your production values
```

3. **Deploy:**
```bash
docker-compose up -d
```

4. **Verify deployment:**
```bash
docker-compose ps
docker-compose logs
```

## Service Configuration

### Database Initialization

The databases are automatically initialized with sample data. For production:

1. **MySQL**: Update `databases/mysql/init.sql` with your schema
2. **MongoDB**: Update `databases/mongodb/init.js` with your collections

### SSL/TLS Configuration

For production, configure SSL certificates:

1. **Using Let's Encrypt:**
```bash
# Install certbot
sudo apt install certbot

# Get certificates
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com
```

2. **Update nginx configuration:**
```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ {
        proxy_pass http://api-gateway:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Monitoring and Logging

### Health Checks

All services include health check endpoints:
- API Gateway: `GET /health`
- Backend: `GET /health`
- Microservices: `GET /health`

### Log Management

View logs for specific services:
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs backend

# Follow logs
docker-compose logs -f api-gateway
```

### Monitoring Setup

1. **Add monitoring stack to docker-compose.yml:**
```yaml
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## Backup and Recovery

### Database Backups

1. **MySQL Backup:**
```bash
docker exec vtravels_mysql mysqldump -u root -p vtravels > backup_$(date +%Y%m%d).sql
```

2. **MongoDB Backup:**
```bash
docker exec vtravels_mongodb mongodump --out /backup/$(date +%Y%m%d)
```

### Automated Backups

Create a backup script:
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# MySQL backup
docker exec vtravels_mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD vtravels > $BACKUP_DIR/mysql_backup.sql

# MongoDB backup
docker exec vtravels_mongodb mongodump --out $BACKUP_DIR/mongodb

# Compress backups
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

# Upload to cloud storage (optional)
aws s3 cp $BACKUP_DIR.tar.gz s3://your-backup-bucket/
```

Add to crontab:
```bash
# Daily backup at 2 AM
0 2 * * * /path/to/backup.sh
```

## Scaling

### Horizontal Scaling

Scale specific services:
```bash
# Scale microservices
docker-compose up -d --scale travel-search=3 --scale recommendations=2

# Scale with load balancer
docker-compose -f docker-compose.yml -f docker-compose.scale.yml up -d
```

### Load Balancing

Add nginx load balancer configuration:
```nginx
upstream backend {
    server backend1:8000;
    server backend2:8000;
    server backend3:8000;
}

upstream api_gateway {
    server api-gateway1:8080;
    server api-gateway2:8080;
}
```

## Troubleshooting

### Common Issues

1. **Port conflicts:**
```bash
# Check port usage
sudo netstat -tulpn | grep :3000

# Stop conflicting services
sudo systemctl stop apache2
```

2. **Memory issues:**
```bash
# Check memory usage
docker stats

# Increase Docker memory limit
# Docker Desktop: Settings > Resources > Memory
```

3. **Database connection issues:**
```bash
# Check database logs
docker-compose logs mysql
docker-compose logs mongodb

# Test connections
docker exec -it vtravels_mysql mysql -u root -p
docker exec -it vtravels_mongodb mongo
```

### Performance Optimization

1. **Database optimization:**
   - Add proper indexes
   - Configure connection pooling
   - Enable query caching

2. **Application optimization:**
   - Enable Redis caching
   - Configure CDN for static assets
   - Implement API response caching

3. **Infrastructure optimization:**
   - Use SSD storage
   - Configure proper resource limits
   - Implement horizontal scaling

## Security Considerations

1. **Network Security:**
   - Use private networks for internal communication
   - Implement firewall rules
   - Regular security updates

2. **Application Security:**
   - Strong JWT secrets
   - Input validation
   - Rate limiting
   - HTTPS enforcement

3. **Database Security:**
   - Strong passwords
   - Network isolation
   - Regular backups
   - Encryption at rest

## Maintenance

### Regular Tasks

1. **Weekly:**
   - Check logs for errors
   - Monitor resource usage
   - Verify backups

2. **Monthly:**
   - Update dependencies
   - Security patches
   - Performance review

3. **Quarterly:**
   - Full system backup
   - Disaster recovery test
   - Security audit

### Updates

Update the application:
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart services
docker-compose build
docker-compose up -d

# Clean up old images
docker image prune -f
```

## Support

For deployment support:
- Documentation: https://docs.vtravels.com
- Issues: https://github.com/vtravels/vtravels/issues
- Email: <EMAIL>
