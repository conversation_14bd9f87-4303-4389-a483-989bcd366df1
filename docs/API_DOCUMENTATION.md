# VTravels API Documentation

## Overview

VTravels is a comprehensive travel booking platform with a microservices architecture. The API provides endpoints for user management, travel search, bookings, reviews, and recommendations.

## Architecture

- **API Gateway**: Routes requests to appropriate services (Port: 8080)
- **Python Backend**: User management, authentication, bookings (Port: 8000)
- **Travel Search Service**: Flight, hotel, and package search (Port: 3001)
- **Recommendations Service**: Personalized travel recommendations (Port: 3002)
- **Notifications Service**: Email, SMS, and push notifications (Port: 3003)
- **Reviews Service**: User reviews and ratings (Port: 3004)

## Base URLs

- **Production**: `https://api.vtravels.com`
- **Development**: `http://localhost:8080`

## Authentication

Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## API Endpoints

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "is_active": true,
    "is_verified": false
  }
}
```

#### Login User
```http
POST /api/v1/auth/login
```

#### Get Current User
```http
GET /api/v1/auth/me
Authorization: Bearer <token>
```

#### Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer <token>
```

### Search Endpoints

#### Search Destinations
```http
GET /api/search/destinations?q=paris&limit=20&offset=0
```

**Query Parameters:**
- `q` (optional): Search query
- `country` (optional): Filter by country
- `limit` (optional): Number of results (default: 20, max: 50)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "destinations": [
    {
      "id": "paris",
      "name": "Paris",
      "description": "The City of Light...",
      "city": "Paris",
      "country": "France",
      "rating": 4.7,
      "image_urls": ["https://example.com/paris.jpg"],
      "attractions": ["Eiffel Tower", "Louvre Museum"]
    }
  ],
  "pagination": {
    "total": 100,
    "limit": 20,
    "offset": 0,
    "hasMore": true
  }
}
```

#### Search Flights
```http
GET /api/search/flights/search?departure_airport=JFK&arrival_airport=LAX&departure_date=2024-12-01&passengers=2
```

**Required Parameters:**
- `departure_airport`: 3-letter airport code
- `arrival_airport`: 3-letter airport code
- `departure_date`: ISO date format (YYYY-MM-DD)

**Optional Parameters:**
- `return_date`: ISO date format for round-trip
- `passengers`: Number of passengers (default: 1)
- `travel_class`: economy, premium_economy, business, first
- `max_price`: Maximum price filter
- `max_stops`: Maximum number of stops

#### Search Hotels
```http
GET /api/search/hotels/search?destination=paris&check_in=2024-12-01&check_out=2024-12-05&guests=2
```

**Required Parameters:**
- `check_in`: ISO date format
- `check_out`: ISO date format

**Optional Parameters:**
- `destination`: Destination name or ID
- `guests`: Number of guests (default: 2)
- `rooms`: Number of rooms (default: 1)
- `min_price`, `max_price`: Price range
- `star_rating`: Hotel star rating (1-5)

### Booking Endpoints

#### Get User Bookings
```http
GET /api/v1/bookings?skip=0&limit=20
Authorization: Bearer <token>
```

#### Get Booking Details
```http
GET /api/v1/bookings/{booking_id}
Authorization: Bearer <token>
```

#### Cancel Booking
```http
PUT /api/v1/bookings/{booking_id}/cancel
Authorization: Bearer <token>
```

### Review Endpoints

#### Get Reviews
```http
GET /api/reviews?entity_type=destination&entity_id=paris&limit=20
```

#### Create Review
```http
POST /api/reviews
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "user_id": 1,
  "entity_type": "destination",
  "entity_id": "paris",
  "rating": 5,
  "title": "Amazing city!",
  "comment": "Paris exceeded all my expectations...",
  "travel_date": "2024-09-15"
}
```

#### Get Review Statistics
```http
GET /api/reviews/stats/{entity_type}/{entity_id}
```

### Recommendation Endpoints

#### Get User Recommendations
```http
GET /api/recommendations/{user_id}
Authorization: Bearer <token>
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": true,
  "message": "Error description",
  "error_code": "ERROR_CODE",
  "details": {}
}
```

### Common Error Codes

- `400`: Bad Request - Invalid parameters
- `401`: Unauthorized - Missing or invalid token
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `422`: Validation Error - Invalid data format
- `500`: Internal Server Error

## Rate Limiting

API requests are rate-limited:
- **General endpoints**: 1000 requests per 15 minutes per IP
- **Search endpoints**: 100 requests per 15 minutes per IP
- **Authentication endpoints**: 10 requests per minute per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)

## Pagination

List endpoints support pagination with these parameters:
- `limit`: Number of items per page (max: 100)
- `offset`: Number of items to skip

Pagination info is included in responses:
```json
{
  "pagination": {
    "total": 500,
    "limit": 20,
    "offset": 0,
    "hasMore": true
  }
}
```

## Data Formats

- **Dates**: ISO 8601 format (YYYY-MM-DD)
- **Timestamps**: ISO 8601 with timezone (2024-01-01T12:00:00Z)
- **Currency**: 3-letter ISO currency codes (USD, EUR, etc.)
- **Countries**: 2-letter ISO country codes (US, FR, etc.)

## SDKs and Libraries

Official SDKs are available for:
- JavaScript/TypeScript
- Python
- PHP
- Java

## Support

For API support, contact:
- Email: <EMAIL>
- Documentation: https://docs.vtravels.com
- Status Page: https://status.vtravels.com
