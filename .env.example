# VTravels Environment Configuration Template
# Copy this file to .env and update with your actual values

# Application Environment
NODE_ENV=development
DEBUG=true
ENVIRONMENT=development

# Database Configuration - MySQL
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=vtravels_user
MYSQL_PASSWORD=vtravels_password
MYSQL_DATABASE=vtravels
MYSQL_ROOT_PASSWORD=rootpassword

# Database Configuration - MongoDB
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_USER=admin
MONGODB_PASSWORD=adminpassword
MONGODB_DATABASE=vtravels

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CACHE_TTL=3600

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=VTravels

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# AWS S3 Configuration (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=vtravels-uploads

# Payment Gateway Configuration
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Microservices URLs
BACKEND_URL=http://localhost:8000
TRAVEL_SEARCH_URL=http://localhost:3001
RECOMMENDATIONS_URL=http://localhost:3002
NOTIFICATIONS_URL=http://localhost:3003
REVIEWS_URL=http://localhost:3004

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# CORS Configuration
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8080
REACT_APP_ENVIRONMENT=development

# External API Keys (Optional)
GOOGLE_MAPS_API_KEY=your-google-maps-key
WEATHER_API_KEY=your-weather-api-key
CURRENCY_API_KEY=your-currency-api-key

# Social Login (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Monitoring (Optional)
SENTRY_DSN=your-sentry-dsn
NEW_RELIC_LICENSE_KEY=your-newrelic-key

# Production Security Settings
# Uncomment and configure for production deployment
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/private.key
# SECURE_COOKIES=true
# HTTPS_ONLY=true
