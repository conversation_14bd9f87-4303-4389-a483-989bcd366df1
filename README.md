# VTravels - Full Stack Travel Booking Platform

<div align="center">

![VTravels Logo](https://via.placeholder.com/200x80/1976d2/ffffff?text=VTravels)

**A comprehensive travel booking platform built with modern microservices architecture**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![Python](https://img.shields.io/badge/Python-3.11+-green.svg)](https://www.python.org/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)

</div>

## 🌟 Features

- **🔐 User Authentication** - Secure JWT-based authentication with refresh tokens
- **✈️ Flight Search** - Comprehensive flight search with filters and real-time pricing
- **🏨 Hotel Booking** - Hotel search with location-based filtering and amenities
- **📦 Travel Packages** - Curated travel packages with bundled services
- **⭐ Reviews & Ratings** - User-generated reviews with photo uploads
- **🔔 Real-time Notifications** - Socket.IO powered notifications
- **💡 Personalized Recommendations** - AI-ready recommendation engine
- **📱 Responsive Design** - Mobile-first design with Material-UI
- **🚀 Microservices Architecture** - Scalable and maintainable service architecture
- **🐳 Docker Ready** - Complete containerization with Docker Compose

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Git

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/vtravels.git
cd vtravels
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration (optional for development)
nano .env
```

### 3. Start the Application

```bash
# Using the startup script (recommended)
./scripts/start-dev.sh

# Or using Docker Compose directly
docker-compose -f docker-compose.dev.yml up -d

# Or using Make
make dev
```

### 4. Access the Application

- **Frontend**: http://localhost:3000
- **API Gateway**: http://localhost:8080
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📋 Available Commands

### Using Make (Recommended)

```bash
# Development
make dev              # Start development environment
make dev-build        # Build and start development
make dev-logs         # View development logs

# Testing
make test             # Run all tests
make test-backend     # Run backend tests only
make test-frontend    # Run frontend tests only

# Utilities
make logs             # View all service logs
make stop             # Stop all services
make restart          # Restart all services
make clean            # Clean up containers and volumes
make health           # Check service health

# Database
make db-backup        # Backup databases
make db-reset         # Reset databases (WARNING: destroys data)
```

## 🛠️ Technology Stack

### Backend Services

| Service | Technology | Port | Purpose |
|---------|------------|------|---------|
| **Python Backend** | FastAPI + SQLAlchemy | 8000 | User management, authentication, bookings |
| **Travel Search** | Node.js + Express + MongoDB | 3001 | Flight, hotel, package search |
| **Recommendations** | Node.js + Express | 3002 | Personalized travel recommendations |
| **Notifications** | Node.js + Socket.IO | 3003 | Real-time notifications |
| **Reviews** | Node.js + Express + MongoDB | 3004 | User reviews and ratings |
| **API Gateway** | Node.js + Express | 8080 | Request routing and load balancing |

### Databases

- **MySQL 8.0** - User accounts, bookings, payments
- **MongoDB 6.0** - Travel data, reviews, search history
- **Redis 7.0** - Caching, sessions, real-time data

### Frontend

- **React 18** with TypeScript
- **Material-UI (MUI)** for components
- **Redux Toolkit** for state management
- **React Query** for API data fetching
- **React Router** for navigation

## 📁 Project Structure

```
vtravels/
├── 📁 backend/                 # Python FastAPI backend
│   ├── 📁 app/
│   │   ├── 📁 core/           # Configuration, database
│   │   ├── 📁 models/         # SQLAlchemy models
│   │   ├── 📁 routes/         # API endpoints
│   │   ├── 📁 utils/          # Utilities
│   │   └── 📄 main.py         # FastAPI app
│   ├── 📁 tests/              # Backend tests
│   └── 📄 requirements.txt    # Python dependencies
├── 📁 microservices/          # Node.js microservices
│   ├── 📁 travel-search/      # Search service
│   ├── 📁 recommendations/    # Recommendations service
│   ├── 📁 notifications/      # Notifications service
│   └── 📁 reviews/            # Reviews service
├── 📁 frontend/               # React frontend
│   ├── 📁 src/
│   │   ├── 📁 components/     # UI components
│   │   ├── 📁 pages/          # Application pages
│   │   ├── 📁 store/          # Redux store
│   │   └── 📁 services/       # API services
│   └── 📄 package.json
├── 📁 api-gateway/            # API Gateway
├── 📁 databases/              # Database initialization
├── 📁 docs/                   # Documentation
├── 📁 scripts/                # Utility scripts
├── 📄 docker-compose.yml      # Production setup
├── 📄 docker-compose.dev.yml  # Development setup
└── 📄 Makefile               # Development commands
```
