# VTravels - Full Stack Travel Application

## 🎯 Project Overview

VTravels is a comprehensive, full-stack travel booking platform built with modern technologies and microservices architecture. The application provides users with the ability to search, book, and manage travel arrangements including flights, hotels, and travel packages.

## 🏗️ Architecture

### Technology Stack

**Backend Services:**
- **Python FastAPI** - Main backend API for user management, authentication, and bookings
- **Node.js Microservices** - Specialized services for travel search, recommendations, notifications, and reviews
- **API Gateway** - Central routing and load balancing

**Databases:**
- **MySQL** - User accounts, bookings, payments, and transactional data
- **MongoDB** - Travel destinations, hotels, flights, reviews, and search data
- **Redis** - Caching, session management, and real-time data

**Frontend:**
- **React 18** with TypeScript
- **Material-UI** for modern, responsive design
- **Redux Toolkit** for state management
- **React Query** for efficient API data fetching

**Infrastructure:**
- **Docker** containerization for all services
- **Docker Compose** for orchestration
- **Nginx** for reverse proxy and static file serving

## 📁 Project Structure

```
vtravels/
├── backend/                    # Python FastAPI backend
│   ├── app/
│   │   ├── core/              # Configuration, database, Redis
│   │   ├── models/            # SQLAlchemy models
│   │   ├── routes/            # API endpoints
│   │   ├── utils/             # Utilities and helpers
│   │   └── main.py            # FastAPI application
│   ├── tests/                 # Backend tests
│   ├── requirements.txt       # Python dependencies
│   └── Dockerfile
├── microservices/             # Node.js microservices
│   ├── travel-search/         # Flight, hotel, package search
│   ├── recommendations/       # Personalized recommendations
│   ├── notifications/         # Email, SMS, push notifications
│   └── reviews/               # User reviews and ratings
├── frontend/                  # React TypeScript frontend
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   ├── pages/             # Application pages
│   │   ├── store/             # Redux store and slices
│   │   ├── services/          # API service layer
│   │   └── theme/             # Material-UI theme
│   ├── public/
│   ├── package.json
│   └── Dockerfile
├── api-gateway/               # API Gateway service
├── databases/                 # Database initialization
│   ├── mysql/                 # MySQL schema and seed data
│   └── mongodb/               # MongoDB collections and seed data
├── docs/                      # Documentation
├── scripts/                   # Utility scripts
├── docker-compose.yml         # Production orchestration
├── docker-compose.dev.yml     # Development orchestration
└── README.md
```

## 🚀 Key Features

### User Management
- ✅ User registration and authentication with JWT
- ✅ Profile management and preferences
- ✅ Session management with Redis
- ✅ Password security with bcrypt hashing

### Travel Search
- ✅ Flight search with multiple filters (price, stops, airline, class)
- ✅ Hotel search with location, dates, and amenities
- ✅ Travel package browsing and filtering
- ✅ Destination search with autocomplete
- ✅ Popular destinations and recommendations

### Booking Management
- ✅ Booking creation and management
- ✅ Booking status tracking
- ✅ Cancellation and modification support
- ✅ Passenger information management

### Reviews & Ratings
- ✅ User reviews for destinations, hotels, and services
- ✅ Rating system with statistics
- ✅ Review helpfulness voting
- ✅ Photo uploads for reviews

### Notifications
- ✅ Real-time notifications with Socket.IO
- ✅ Email notifications for bookings
- ✅ Push notification support
- ✅ Notification preferences management

### Advanced Features
- ✅ Personalized travel recommendations
- ✅ Multi-currency support
- ✅ Responsive design for all devices
- ✅ API rate limiting and security
- ✅ Comprehensive error handling
- ✅ Health monitoring for all services

## 🛠️ Development Setup

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd vtravels

# Start development environment
./scripts/start-dev.sh

# Or manually with Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

### Access Points
- **Frontend**: http://localhost:3000
- **API Gateway**: http://localhost:8080
- **Python Backend**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🔧 Service Details

### Python Backend (Port 8000)
- **FastAPI** with automatic OpenAPI documentation
- **SQLAlchemy** ORM with MySQL
- **JWT authentication** with refresh tokens
- **Redis integration** for caching and sessions
- **Comprehensive error handling** and validation

### Travel Search Service (Port 3001)
- **Express.js** with MongoDB integration
- **Flight search** with real-time availability
- **Hotel search** with location-based filtering
- **Travel package** recommendations
- **Redis caching** for performance

### Recommendations Service (Port 3002)
- **Machine learning-ready** architecture
- **User behavior tracking**
- **Personalized suggestions**
- **A/B testing support**

### Notifications Service (Port 3003)
- **Socket.IO** for real-time notifications
- **Email integration** with templates
- **SMS support** (configurable)
- **Push notification** infrastructure

### Reviews Service (Port 3004)
- **Review management** with moderation
- **Rating aggregation** and statistics
- **Photo upload** support
- **Spam detection** capabilities

## 🗄️ Database Design

### MySQL (Relational Data)
- **Users** - Authentication and profile data
- **Bookings** - Travel bookings and transactions
- **Payments** - Payment processing and history
- **Sessions** - User session management
- **Notifications** - User notification preferences

### MongoDB (Document Data)
- **Destinations** - Travel destination information
- **Hotels** - Hotel details and amenities
- **Flights** - Flight schedules and pricing
- **Reviews** - User reviews and ratings
- **Search History** - User search patterns

## 🔒 Security Features

- **JWT Authentication** with secure token management
- **Password Hashing** with bcrypt
- **Rate Limiting** to prevent abuse
- **Input Validation** and sanitization
- **CORS Configuration** for cross-origin requests
- **Helmet.js** for security headers
- **Environment Variables** for sensitive data

## 📊 Performance Optimizations

- **Redis Caching** for frequently accessed data
- **Database Indexing** for optimal query performance
- **API Response Caching** with TTL
- **Image Optimization** and CDN integration
- **Lazy Loading** for frontend components
- **Connection Pooling** for databases

## 🧪 Testing

### Backend Testing
- **Unit Tests** with pytest
- **Integration Tests** for API endpoints
- **Database Tests** with test fixtures
- **Authentication Tests** for security

### Frontend Testing
- **Component Tests** with React Testing Library
- **Integration Tests** for user flows
- **E2E Tests** with Cypress (configurable)

### Microservices Testing
- **Unit Tests** with Jest
- **API Tests** with Supertest
- **Mock Data** for isolated testing

## 📈 Monitoring & Observability

- **Health Check Endpoints** for all services
- **Structured Logging** with Winston/Python logging
- **Error Tracking** with detailed stack traces
- **Performance Metrics** collection ready
- **Docker Health Checks** for container monitoring

## 🚀 Deployment

### Development
```bash
docker-compose -f docker-compose.dev.yml up -d
```

### Production
```bash
docker-compose up -d
```

### Scaling
```bash
docker-compose up -d --scale travel-search=3 --scale recommendations=2
```

## 📚 Documentation

- **API Documentation**: Comprehensive OpenAPI/Swagger docs
- **Deployment Guide**: Step-by-step deployment instructions
- **Architecture Guide**: Detailed system architecture
- **Development Guide**: Local development setup

## 🔮 Future Enhancements

### Planned Features
- **Payment Integration** (Stripe, PayPal)
- **Social Login** (Google, Facebook, Apple)
- **Mobile App** (React Native)
- **Advanced Analytics** dashboard
- **Multi-language Support** (i18n)
- **Travel Insurance** integration
- **Loyalty Program** management

### Technical Improvements
- **Kubernetes** deployment
- **GraphQL** API layer
- **Event Sourcing** for audit trails
- **Machine Learning** recommendations
- **Elasticsearch** for advanced search
- **Message Queues** (RabbitMQ/Apache Kafka)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: https://docs.vtravels.com
- **Issues**: GitHub Issues
- **Email**: <EMAIL>

---

**Built with ❤️ by the VTravels Team**
