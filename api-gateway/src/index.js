/**
 * VTravels API Gateway
 * Routes requests to appropriate microservices and handles cross-cutting concerns
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 8080;

// Logger setup
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/gateway.log' })
  ]
});

// Service URLs from environment variables
const services = {
  backend: process.env.BACKEND_URL || 'http://localhost:8000',
  travelSearch: process.env.TRAVEL_SEARCH_URL || 'http://localhost:3001',
  recommendations: process.env.RECOMMENDATIONS_URL || 'http://localhost:3002',
  notifications: process.env.NOTIFICATIONS_URL || 'http://localhost:3003',
  reviews: process.env.REVIEWS_URL || 'http://localhost:3004'
};

// Middleware
app.use(helmet());
app.use(compression());

// CORS configuration
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: {
    error: true,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use(limiter);

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  const healthChecks = {};
  
  // Check all services
  for (const [serviceName, serviceUrl] of Object.entries(services)) {
    try {
      const axios = require('axios');
      const response = await axios.get(`${serviceUrl}/health`, { timeout: 5000 });
      healthChecks[serviceName] = {
        status: 'healthy',
        url: serviceUrl,
        responseTime: response.headers['x-response-time'] || 'N/A'
      };
    } catch (error) {
      healthChecks[serviceName] = {
        status: 'unhealthy',
        url: serviceUrl,
        error: error.message
      };
    }
  }
  
  const overallStatus = Object.values(healthChecks).every(check => check.status === 'healthy') 
    ? 'healthy' : 'degraded';
  
  res.json({
    status: overallStatus,
    timestamp: new Date().toISOString(),
    services: healthChecks
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'VTravels API Gateway',
    version: '1.0.0',
    services: Object.keys(services),
    endpoints: {
      health: '/health',
      backend: '/api/v1/*',
      search: '/api/search/*',
      recommendations: '/api/recommendations/*',
      notifications: '/api/notifications/*',
      reviews: '/api/reviews/*'
    }
  });
});

// Proxy configuration options
const createProxyOptions = (target, pathRewrite = {}) => ({
  target,
  changeOrigin: true,
  pathRewrite,
  timeout: 30000,
  proxyTimeout: 30000,
  onError: (err, req, res) => {
    logger.error('Proxy error:', {
      error: err.message,
      target,
      path: req.path,
      method: req.method
    });
    
    if (!res.headersSent) {
      res.status(503).json({
        error: true,
        message: 'Service temporarily unavailable',
        service: target
      });
    }
  },
  onProxyReq: (proxyReq, req, res) => {
    // Add request ID for tracing
    const requestId = req.headers['x-request-id'] || 
      `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    proxyReq.setHeader('X-Request-ID', requestId);
    
    // Forward user information if available
    if (req.headers.authorization) {
      proxyReq.setHeader('Authorization', req.headers.authorization);
    }
  },
  onProxyRes: (proxyRes, req, res) => {
    // Add CORS headers to proxied responses
    proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,PUT,POST,DELETE,OPTIONS';
    proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Content-Length, X-Requested-With';
    
    // Log response
    logger.info(`Proxy response: ${proxyRes.statusCode}`, {
      target,
      path: req.path,
      method: req.method,
      statusCode: proxyRes.statusCode
    });
  }
});

// Route to Python Backend (Authentication, Users, Bookings, Payments)
app.use('/api/v1', createProxyMiddleware(createProxyOptions(services.backend)));

// Route to Travel Search Service
app.use('/api/search', createProxyMiddleware(createProxyOptions(
  services.travelSearch,
  { '^/api/search': '/api' }
)));

// Route to Recommendations Service
app.use('/api/recommendations', createProxyMiddleware(createProxyOptions(
  services.recommendations,
  { '^/api/recommendations': '/api/recommendations' }
)));

// Route to Notifications Service
app.use('/api/notifications', createProxyMiddleware(createProxyOptions(
  services.notifications,
  { '^/api/notifications': '/api/notifications' }
)));

// Route to Reviews Service
app.use('/api/reviews', createProxyMiddleware(createProxyOptions(
  services.reviews,
  { '^/api/reviews': '/api/reviews' }
)));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: true,
    message: 'Endpoint not found',
    path: req.originalUrl,
    availableEndpoints: [
      '/api/v1/* (Backend API)',
      '/api/search/* (Travel Search)',
      '/api/recommendations/* (Recommendations)',
      '/api/notifications/* (Notifications)',
      '/api/reviews/* (Reviews)'
    ]
  });
});

// Global error handler
app.use((error, req, res, next) => {
  logger.error('Gateway error:', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method
  });
  
  res.status(500).json({
    error: true,
    message: 'Internal gateway error',
    ...(process.env.NODE_ENV === 'development' && { details: error.message })
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  logger.info(`API Gateway running on port ${PORT}`);
  logger.info('Service endpoints:', services);
});

module.exports = app;
