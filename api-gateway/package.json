{"name": "vtravels-api-gateway", "version": "1.0.0", "description": "API Gateway for VTravels microservices", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}