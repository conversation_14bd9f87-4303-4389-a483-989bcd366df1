#!/bin/bash

# VTravels Development Environment Startup Script

set -e

echo "🚀 Starting VTravels Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Pull latest images
echo "📥 Pulling latest images..."
docker-compose -f docker-compose.dev.yml pull

# Build services
echo "🔨 Building services..."
docker-compose -f docker-compose.dev.yml build

# Start services
echo "🚀 Starting services..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

services=("mysql" "mongodb" "redis" "backend" "travel-search" "recommendations" "notifications" "reviews" "api-gateway" "frontend")

for service in "${services[@]}"; do
    if docker-compose -f docker-compose.dev.yml ps | grep -q "${service}.*Up"; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
        docker-compose -f docker-compose.dev.yml logs $service
    fi
done

echo ""
echo "🎉 VTravels Development Environment is ready!"
echo ""
echo "📱 Frontend:        http://localhost:3000"
echo "🌐 API Gateway:     http://localhost:8080"
echo "🐍 Python Backend:  http://localhost:8000"
echo "📚 API Docs:        http://localhost:8000/docs"
echo "🔍 Travel Search:   http://localhost:3001"
echo "💡 Recommendations: http://localhost:3002"
echo "📢 Notifications:   http://localhost:3003"
echo "⭐ Reviews:         http://localhost:3004"
echo ""
echo "🗄️  Database Access:"
echo "   MySQL:    localhost:3306 (user: vtravels_user, password: vtravels_password)"
echo "   MongoDB:  localhost:27017 (user: admin, password: adminpassword)"
echo "   Redis:    localhost:6379"
echo ""
echo "📋 Useful commands:"
echo "   View logs:        docker-compose -f docker-compose.dev.yml logs -f"
echo "   Stop services:    docker-compose -f docker-compose.dev.yml down"
echo "   Restart service:  docker-compose -f docker-compose.dev.yml restart <service>"
echo ""
echo "Happy coding! 🎯"
