/**
 * VTravels Recommendations Microservice
 * Handles personalized travel recommendations
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'recommendations',
    version: '1.0.0'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'VTravels Recommendations Microservice',
    version: '1.0.0'
  });
});

// Mock recommendations endpoint
app.get('/api/recommendations/:userId', (req, res) => {
  const { userId } = req.params;
  
  // Mock recommendations
  const recommendations = [
    {
      id: '1',
      type: 'destination',
      title: 'Paris, France',
      description: 'Based on your interest in European culture',
      image: 'https://example.com/paris.jpg',
      score: 0.95
    },
    {
      id: '2',
      type: 'hotel',
      title: 'Luxury Hotel in Tokyo',
      description: 'Perfect for your upcoming Japan trip',
      image: 'https://example.com/tokyo-hotel.jpg',
      score: 0.88
    }
  ];
  
  res.json({
    user_id: userId,
    recommendations
  });
});

app.listen(PORT, () => {
  console.log(`Recommendations service running on port ${PORT}`);
});
