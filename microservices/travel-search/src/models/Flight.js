/**
 * Flight model for Travel Search Service
 */

const mongoose = require('mongoose');

const flightSchema = new mongoose.Schema({
  airline: {
    code: {
      type: String,
      required: true,
      index: true
    },
    name: {
      type: String,
      required: true
    }
  },
  flight_number: {
    type: String,
    required: true,
    index: true
  },
  departure: {
    airport_code: {
      type: String,
      required: true,
      index: true
    },
    airport_name: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      required: true,
      index: true
    },
    time: {
      type: String,
      required: true
    }
  },
  arrival: {
    airport_code: {
      type: String,
      required: true,
      index: true
    },
    airport_name: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      required: true
    },
    time: {
      type: String,
      required: true
    }
  },
  duration: {
    type: Number, // in minutes
    required: true,
    index: true
  },
  aircraft: {
    type: String,
    required: true
  },
  price: {
    economy: {
      type: Number,
      min: 0,
      index: true
    },
    premium_economy: {
      type: Number,
      min: 0
    },
    business: {
      type: Number,
      min: 0
    },
    first: {
      type: Number,
      min: 0
    }
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  available_seats: {
    economy: {
      type: Number,
      min: 0,
      default: 0
    },
    premium_economy: {
      type: Number,
      min: 0,
      default: 0
    },
    business: {
      type: Number,
      min: 0,
      default: 0
    },
    first: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  stops: {
    type: Number,
    min: 0,
    default: 0,
    index: true
  },
  layovers: [{
    airport_code: String,
    airport_name: String,
    city: String,
    duration: Number // in minutes
  }],
  amenities: [{
    type: String
  }],
  baggage: {
    carry_on: {
      included: Boolean,
      weight_limit: Number,
      size_limit: String
    },
    checked: {
      included: Boolean,
      weight_limit: Number,
      additional_fee: Number
    }
  },
  cancellation_policy: {
    refundable: {
      type: Boolean,
      default: false
    },
    change_fee: {
      type: Number,
      min: 0
    }
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

// Create indexes
flightSchema.index({ airline: 1 });
flightSchema.index({ 'departure.airport_code': 1, 'arrival.airport_code': 1 });
flightSchema.index({ 'departure.date': 1 });
flightSchema.index({ 'price.economy': 1 });
flightSchema.index({ duration: 1 });
flightSchema.index({ stops: 1 });

// Virtual for route
flightSchema.virtual('route').get(function() {
  return `${this.departure.airport_code} → ${this.arrival.airport_code}`;
});

// Virtual for formatted duration
flightSchema.virtual('formatted_duration').get(function() {
  const hours = Math.floor(this.duration / 60);
  const minutes = this.duration % 60;
  return `${hours}h ${minutes}m`;
});

// Method to check seat availability
flightSchema.methods.checkSeatAvailability = function(travelClass, passengers = 1) {
  return this.available_seats[travelClass] >= passengers;
};

// Method to get price for travel class
flightSchema.methods.getPriceForClass = function(travelClass) {
  return this.price[travelClass] || null;
};

// Static method to search flights
flightSchema.statics.searchFlights = function(query, options = {}) {
  const {
    limit = 20,
    offset = 0,
    sortBy = 'price.economy',
    sortOrder = 1
  } = options;

  let searchQuery = {};
  
  if (query.departure_airport) {
    searchQuery['departure.airport_code'] = query.departure_airport;
  }
  
  if (query.arrival_airport) {
    searchQuery['arrival.airport_code'] = query.arrival_airport;
  }
  
  if (query.departure_date) {
    const startDate = new Date(query.departure_date);
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 1);
    
    searchQuery['departure.date'] = {
      $gte: startDate,
      $lt: endDate
    };
  }
  
  if (query.max_price) {
    searchQuery['price.economy'] = { $lte: query.max_price };
  }
  
  if (query.max_stops !== undefined) {
    searchQuery.stops = { $lte: query.max_stops };
  }
  
  if (query.airline) {
    searchQuery['airline.code'] = query.airline;
  }
  
  if (query.max_duration) {
    searchQuery.duration = { $lte: query.max_duration };
  }

  return this.find(searchQuery)
    .sort({ [sortBy]: sortOrder })
    .limit(limit)
    .skip(offset);
};

module.exports = mongoose.model('Flight', flightSchema);
