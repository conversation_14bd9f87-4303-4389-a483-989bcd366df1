/**
 * Destination model for Travel Search Service
 */

const mongoose = require('mongoose');

const destinationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  description: {
    type: String,
    required: true
  },
  city: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  country: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true
    },
    coordinates: {
      type: [Number],
      required: true
    }
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0,
    index: true
  },
  popular: {
    type: Boolean,
    default: false,
    index: true
  },
  image_urls: [{
    type: String
  }],
  attractions: [{
    type: String
  }],
  best_time_to_visit: [{
    type: String
  }],
  average_temperature: {
    summer: String,
    winter: String
  },
  currency: {
    type: String,
    required: true
  },
  language: {
    type: String,
    required: true
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

// Create indexes
destinationSchema.index({ location: '2dsphere' });
destinationSchema.index({ name: 'text', description: 'text', city: 'text', country: 'text' });
destinationSchema.index({ country: 1, city: 1 });
destinationSchema.index({ rating: -1 });
destinationSchema.index({ popular: -1 });

// Virtual for formatted location
destinationSchema.virtual('formatted_location').get(function() {
  return `${this.city}, ${this.country}`;
});

// Method to get nearby destinations
destinationSchema.methods.getNearbyDestinations = function(maxDistance = 100000) {
  return this.constructor.find({
    _id: { $ne: this._id },
    location: {
      $near: {
        $geometry: this.location,
        $maxDistance: maxDistance
      }
    }
  }).limit(5);
};

// Static method to search destinations
destinationSchema.statics.searchDestinations = function(query, options = {}) {
  const {
    limit = 20,
    offset = 0,
    sortBy = 'rating',
    sortOrder = -1
  } = options;

  let searchQuery = {};
  
  if (query.text) {
    searchQuery.$text = { $search: query.text };
  }
  
  if (query.country) {
    searchQuery.country = new RegExp(query.country, 'i');
  }
  
  if (query.minRating) {
    searchQuery.rating = { $gte: query.minRating };
  }
  
  if (query.popular !== undefined) {
    searchQuery.popular = query.popular;
  }

  return this.find(searchQuery)
    .sort({ [sortBy]: sortOrder })
    .limit(limit)
    .skip(offset);
};

module.exports = mongoose.model('Destination', destinationSchema);
