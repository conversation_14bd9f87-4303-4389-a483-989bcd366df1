/**
 * Hotel model for Travel Search Service
 */

const mongoose = require('mongoose');

const roomTypeSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  capacity: {
    type: Number,
    required: true,
    min: 1
  },
  amenities: [{
    type: String
  }]
});

const hotelSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  description: {
    type: String,
    required: true
  },
  destination_id: {
    type: String,
    required: true,
    index: true
  },
  address: {
    street: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true,
      index: true
    },
    country: {
      type: String,
      required: true
    },
    postal_code: String
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true
    },
    coordinates: {
      type: [Number],
      required: true
    }
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0,
    index: true
  },
  star_rating: {
    type: Number,
    min: 1,
    max: 5,
    required: true
  },
  price_range: {
    min: {
      type: Number,
      required: true,
      min: 0
    },
    max: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      required: true,
      default: 'USD'
    }
  },
  amenities: [{
    type: String
  }],
  room_types: [roomTypeSchema],
  images: [{
    type: String
  }],
  policies: {
    check_in: String,
    check_out: String,
    cancellation: String
  },
  availability: {
    type: Boolean,
    default: true,
    index: true
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

// Create indexes
hotelSchema.index({ location: '2dsphere' });
hotelSchema.index({ name: 'text', description: 'text', 'address.city': 'text' });
hotelSchema.index({ destination_id: 1 });
hotelSchema.index({ rating: -1 });
hotelSchema.index({ 'price_range.min': 1, 'price_range.max': 1 });
hotelSchema.index({ amenities: 1 });
hotelSchema.index({ star_rating: 1 });
hotelSchema.index({ availability: 1 });

// Virtual for formatted address
hotelSchema.virtual('formatted_address').get(function() {
  const addr = this.address;
  return `${addr.street}, ${addr.city}, ${addr.country}${addr.postal_code ? ' ' + addr.postal_code : ''}`;
});

// Virtual for average room price
hotelSchema.virtual('average_room_price').get(function() {
  if (this.room_types && this.room_types.length > 0) {
    const total = this.room_types.reduce((sum, room) => sum + room.price, 0);
    return Math.round(total / this.room_types.length);
  }
  return (this.price_range.min + this.price_range.max) / 2;
});

// Method to check availability for dates
hotelSchema.methods.checkAvailability = function(checkIn, checkOut, roomType = null) {
  // This would integrate with a booking system
  // For now, return basic availability
  return this.availability;
};

// Method to get nearby hotels
hotelSchema.methods.getNearbyHotels = function(maxDistance = 5000) {
  return this.constructor.find({
    _id: { $ne: this._id },
    location: {
      $near: {
        $geometry: this.location,
        $maxDistance: maxDistance
      }
    },
    availability: true
  }).limit(5);
};

// Static method to search hotels
hotelSchema.statics.searchHotels = function(query, options = {}) {
  const {
    limit = 20,
    offset = 0,
    sortBy = 'rating',
    sortOrder = -1
  } = options;

  let searchQuery = { availability: true };
  
  if (query.text) {
    searchQuery.$text = { $search: query.text };
  }
  
  if (query.destination_id) {
    searchQuery.destination_id = query.destination_id;
  }
  
  if (query.city) {
    searchQuery['address.city'] = new RegExp(query.city, 'i');
  }
  
  if (query.minRating) {
    searchQuery.rating = { $gte: query.minRating };
  }
  
  if (query.starRating) {
    searchQuery.star_rating = query.starRating;
  }
  
  if (query.minPrice || query.maxPrice) {
    searchQuery['price_range.min'] = {};
    if (query.minPrice) {
      searchQuery['price_range.min'].$gte = query.minPrice;
    }
    if (query.maxPrice) {
      searchQuery['price_range.max'] = { $lte: query.maxPrice };
    }
  }
  
  if (query.amenities && query.amenities.length > 0) {
    searchQuery.amenities = { $in: query.amenities };
  }

  return this.find(searchQuery)
    .sort({ [sortBy]: sortOrder })
    .limit(limit)
    .skip(offset);
};

module.exports = mongoose.model('Hotel', hotelSchema);
