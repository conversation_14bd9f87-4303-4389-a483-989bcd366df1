/**
 * Hotel search routes for Travel Search Service
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');
const Hotel = require('../models/Hotel');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: true,
      message: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

/**
 * GET /api/hotels/search
 * Search hotels
 */
router.get('/search', [
  query('destination').optional().isLength({ min: 2 }).withMessage('Destination must be at least 2 characters'),
  query('check_in').isISO8601().withMessage('Check-in date must be in ISO format'),
  query('check_out').isISO8601().withMessage('Check-out date must be in ISO format'),
  query('guests').optional().isInt({ min: 1, max: 10 }).withMessage('Guests must be between 1 and 10'),
  query('rooms').optional().isInt({ min: 1, max: 5 }).withMessage('Rooms must be between 1 and 5'),
  query('min_price').optional().isFloat({ min: 0 }).withMessage('Min price must be positive'),
  query('max_price').optional().isFloat({ min: 0 }).withMessage('Max price must be positive'),
  query('star_rating').optional().isInt({ min: 1, max: 5 }).withMessage('Star rating must be between 1 and 5'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
], handleValidationErrors, async (req, res, next) => {
  try {
    const {
      destination,
      check_in,
      check_out,
      guests = 2,
      rooms = 1,
      min_price,
      max_price,
      star_rating,
      amenities,
      limit = 20,
      offset = 0
    } = req.query;
    
    // Create cache key
    const cacheKey = `hotels:${JSON.stringify({
      destination,
      check_in,
      check_out,
      guests,
      rooms,
      min_price,
      max_price,
      star_rating,
      amenities,
      limit,
      offset
    })}`;
    
    // Check cache first
    const cachedResults = await cache.get(cacheKey);
    if (cachedResults) {
      logger.info('Returning cached hotel results');
      return res.json(cachedResults);
    }
    
    // Build search query
    const searchQuery = {
      city: destination,
      minPrice: min_price,
      maxPrice: max_price,
      starRating: star_rating,
      amenities: amenities ? amenities.split(',') : undefined
    };
    
    const hotels = await Hotel.searchHotels(searchQuery, {
      limit: parseInt(limit),
      offset: parseInt(offset),
      sortBy: 'rating',
      sortOrder: -1
    });
    
    const total = await Hotel.countDocuments({
      'address.city': new RegExp(destination, 'i'),
      availability: true
    });
    
    const results = {
      hotels: hotels.map(hotel => ({
        id: hotel._id,
        name: hotel.name,
        description: hotel.description,
        address: hotel.address,
        formatted_address: hotel.formatted_address,
        rating: hotel.rating,
        star_rating: hotel.star_rating,
        price_range: hotel.price_range,
        average_room_price: hotel.average_room_price,
        amenities: hotel.amenities,
        images: hotel.images,
        policies: hotel.policies
      })),
      search_params: {
        destination,
        check_in,
        check_out,
        guests: parseInt(guests),
        rooms: parseInt(rooms)
      },
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < total
      }
    };
    
    // Cache results for 30 minutes
    await cache.set(cacheKey, results, 1800);
    
    res.json(results);
    
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/hotels/:id
 * Get hotel details
 */
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const hotel = await Hotel.findById(id);
    
    if (!hotel) {
      return res.status(404).json({
        error: true,
        message: 'Hotel not found'
      });
    }
    
    res.json({
      id: hotel._id,
      name: hotel.name,
      description: hotel.description,
      address: hotel.address,
      formatted_address: hotel.formatted_address,
      rating: hotel.rating,
      star_rating: hotel.star_rating,
      price_range: hotel.price_range,
      amenities: hotel.amenities,
      room_types: hotel.room_types,
      images: hotel.images,
      policies: hotel.policies
    });
    
  } catch (error) {
    next(error);
  }
});

module.exports = router;
