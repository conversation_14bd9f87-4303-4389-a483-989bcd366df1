/**
 * General search routes for Travel Search Service
 */

const express = require('express');
const { body, query, validationResult } = require('express-validator');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');
const Destination = require('../models/Destination');
const Hotel = require('../models/Hotel');
const Flight = require('../models/Flight');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: true,
      message: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

/**
 * GET /api/search/destinations
 * Search destinations by name, city, or country
 */
router.get('/destinations', [
  query('q').optional().isLength({ min: 2 }).withMessage('Query must be at least 2 characters'),
  query('country').optional().isLength({ min: 2 }).withMessage('Country must be at least 2 characters'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
], handleValidationErrors, async (req, res, next) => {
  try {
    const { q, country, limit = 20, offset = 0 } = req.query;
    
    // Create cache key
    const cacheKey = `destinations:${JSON.stringify({ q, country, limit, offset })}`;
    
    // Check cache first
    const cachedResults = await cache.get(cacheKey);
    if (cachedResults) {
      logger.info('Returning cached destination results');
      return res.json(cachedResults);
    }
    
    // Build search query
    let searchQuery = {};
    
    if (q) {
      searchQuery.$text = { $search: q };
    }
    
    if (country) {
      searchQuery.country = new RegExp(country, 'i');
    }
    
    // Execute search
    const destinations = await Destination.find(searchQuery)
      .select('name description city country location rating image_urls attractions')
      .sort({ rating: -1, popular: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset));
    
    const total = await Destination.countDocuments(searchQuery);
    
    const results = {
      destinations,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < total
      }
    };
    
    // Cache results for 1 hour
    await cache.set(cacheKey, results, 3600);
    
    res.json(results);
    
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/search/autocomplete
 * Autocomplete search for destinations
 */
router.get('/autocomplete', [
  query('q').isLength({ min: 2 }).withMessage('Query must be at least 2 characters'),
  query('limit').optional().isInt({ min: 1, max: 10 }).withMessage('Limit must be between 1 and 10')
], handleValidationErrors, async (req, res, next) => {
  try {
    const { q, limit = 5 } = req.query;
    
    const cacheKey = `autocomplete:${q}:${limit}`;
    
    // Check cache
    const cachedResults = await cache.get(cacheKey);
    if (cachedResults) {
      return res.json(cachedResults);
    }
    
    // Search destinations
    const destinations = await Destination.find({
      $or: [
        { name: new RegExp(q, 'i') },
        { city: new RegExp(q, 'i') },
        { country: new RegExp(q, 'i') }
      ]
    })
    .select('name city country')
    .limit(parseInt(limit));
    
    const suggestions = destinations.map(dest => ({
      id: dest._id,
      name: dest.name,
      city: dest.city,
      country: dest.country,
      display: `${dest.name}, ${dest.city}, ${dest.country}`
    }));
    
    // Cache for 30 minutes
    await cache.set(cacheKey, suggestions, 1800);
    
    res.json(suggestions);
    
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/search/popular
 * Get popular destinations
 */
router.get('/popular', [
  query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20')
], handleValidationErrors, async (req, res, next) => {
  try {
    const { limit = 10 } = req.query;
    
    const cacheKey = `popular_destinations:${limit}`;
    
    // Check cache
    const cachedResults = await cache.get(cacheKey);
    if (cachedResults) {
      return res.json(cachedResults);
    }
    
    const destinations = await Destination.find({ popular: true })
      .select('name description city country location rating image_urls')
      .sort({ rating: -1 })
      .limit(parseInt(limit));
    
    // Cache for 2 hours
    await cache.set(cacheKey, destinations, 7200);
    
    res.json(destinations);
    
  } catch (error) {
    next(error);
  }
});

module.exports = router;
