/**
 * Travel package routes for Travel Search Service
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: true,
      message: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

/**
 * GET /api/packages/search
 * Search travel packages
 */
router.get('/search', [
  query('destination').optional().isLength({ min: 2 }).withMessage('Destination must be at least 2 characters'),
  query('duration').optional().isInt({ min: 1, max: 30 }).withMessage('Duration must be between 1 and 30 days'),
  query('budget').optional().isFloat({ min: 0 }).withMessage('Budget must be positive'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
], handleValidationErrors, async (req, res, next) => {
  try {
    const {
      destination,
      duration,
      budget,
      limit = 20,
      offset = 0
    } = req.query;
    
    // For now, return mock data
    // In a real implementation, this would query a TravelPackage model
    const mockPackages = [
      {
        id: '1',
        name: 'European Grand Tour',
        description: 'Visit 5 amazing European cities in 14 days',
        destinations: ['Paris', 'Rome', 'Barcelona', 'Amsterdam', 'Prague'],
        duration: 14,
        price: 3500,
        currency: 'USD',
        rating: 4.8,
        includes: ['Flights', 'Hotels', 'Tours', 'Breakfast'],
        image: 'https://example.com/europe-tour.jpg'
      },
      {
        id: '2',
        name: 'Asian Adventure',
        description: 'Explore the wonders of Southeast Asia',
        destinations: ['Bangkok', 'Singapore', 'Kuala Lumpur', 'Bali'],
        duration: 10,
        price: 2800,
        currency: 'USD',
        rating: 4.6,
        includes: ['Flights', 'Hotels', 'Tours', 'Some meals'],
        image: 'https://example.com/asia-tour.jpg'
      }
    ];
    
    // Apply filters
    let filteredPackages = mockPackages;
    
    if (destination) {
      filteredPackages = filteredPackages.filter(pkg =>
        pkg.destinations.some(dest =>
          dest.toLowerCase().includes(destination.toLowerCase())
        )
      );
    }
    
    if (duration) {
      filteredPackages = filteredPackages.filter(pkg =>
        Math.abs(pkg.duration - parseInt(duration)) <= 2
      );
    }
    
    if (budget) {
      filteredPackages = filteredPackages.filter(pkg =>
        pkg.price <= parseFloat(budget)
      );
    }
    
    // Apply pagination
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedPackages = filteredPackages.slice(startIndex, endIndex);
    
    const results = {
      packages: paginatedPackages,
      search_params: {
        destination,
        duration: duration ? parseInt(duration) : undefined,
        budget: budget ? parseFloat(budget) : undefined
      },
      pagination: {
        total: filteredPackages.length,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: endIndex < filteredPackages.length
      }
    };
    
    res.json(results);
    
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/packages/:id
 * Get package details
 */
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Mock package details
    const mockPackage = {
      id: id,
      name: 'European Grand Tour',
      description: 'Visit 5 amazing European cities in 14 days with guided tours and luxury accommodations.',
      destinations: ['Paris', 'Rome', 'Barcelona', 'Amsterdam', 'Prague'],
      duration: 14,
      price: 3500,
      currency: 'USD',
      rating: 4.8,
      includes: [
        'Round-trip flights',
        '4-star hotel accommodations',
        'Daily breakfast',
        'Guided city tours',
        'Airport transfers'
      ],
      excludes: [
        'Lunch and dinner',
        'Personal expenses',
        'Travel insurance',
        'Visa fees'
      ],
      itinerary: [
        {
          day: 1,
          city: 'Paris',
          activities: ['Arrival and hotel check-in', 'Welcome dinner']
        },
        {
          day: 2,
          city: 'Paris',
          activities: ['Eiffel Tower visit', 'Seine River cruise', 'Louvre Museum']
        }
      ],
      images: [
        'https://example.com/europe-tour-1.jpg',
        'https://example.com/europe-tour-2.jpg'
      ]
    };
    
    res.json(mockPackage);
    
  } catch (error) {
    next(error);
  }
});

module.exports = router;
