/**
 * Flight search routes for Travel Search Service
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');
const Flight = require('../models/Flight');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: true,
      message: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

/**
 * GET /api/flights/search
 * Search flights
 */
router.get('/search', [
  query('departure_airport').isLength({ min: 3, max: 3 }).withMessage('Departure airport code must be 3 characters'),
  query('arrival_airport').isLength({ min: 3, max: 3 }).withMessage('Arrival airport code must be 3 characters'),
  query('departure_date').isISO8601().withMessage('Departure date must be in ISO format'),
  query('return_date').optional().isISO8601().withMessage('Return date must be in ISO format'),
  query('passengers').optional().isInt({ min: 1, max: 9 }).withMessage('Passengers must be between 1 and 9'),
  query('travel_class').optional().isIn(['economy', 'premium_economy', 'business', 'first']).withMessage('Invalid travel class'),
  query('max_price').optional().isFloat({ min: 0 }).withMessage('Max price must be positive'),
  query('max_stops').optional().isInt({ min: 0, max: 3 }).withMessage('Max stops must be between 0 and 3'),
  query('airline').optional().isLength({ min: 2, max: 3 }).withMessage('Airline code must be 2-3 characters'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
], handleValidationErrors, async (req, res, next) => {
  try {
    const {
      departure_airport,
      arrival_airport,
      departure_date,
      return_date,
      passengers = 1,
      travel_class = 'economy',
      max_price,
      max_stops,
      airline,
      limit = 20,
      offset = 0
    } = req.query;
    
    // Create cache key
    const cacheKey = `flights:${JSON.stringify({
      departure_airport,
      arrival_airport,
      departure_date,
      return_date,
      passengers,
      travel_class,
      max_price,
      max_stops,
      airline,
      limit,
      offset
    })}`;
    
    // Check cache first
    const cachedResults = await cache.get(cacheKey);
    if (cachedResults) {
      logger.info('Returning cached flight results');
      return res.json(cachedResults);
    }
    
    // Search outbound flights
    const outboundQuery = {
      departure_airport,
      arrival_airport,
      departure_date,
      max_price,
      max_stops,
      airline
    };
    
    const outboundFlights = await Flight.searchFlights(outboundQuery, {
      limit: parseInt(limit),
      offset: parseInt(offset),
      sortBy: `price.${travel_class}`,
      sortOrder: 1
    });
    
    let returnFlights = [];
    
    // Search return flights if return date is provided
    if (return_date) {
      const returnQuery = {
        departure_airport: arrival_airport,
        arrival_airport: departure_airport,
        departure_date: return_date,
        max_price,
        max_stops,
        airline
      };
      
      returnFlights = await Flight.searchFlights(returnQuery, {
        limit: parseInt(limit),
        offset: parseInt(offset),
        sortBy: `price.${travel_class}`,
        sortOrder: 1
      });
    }
    
    // Get total count for pagination
    const totalOutbound = await Flight.countDocuments({
      'departure.airport_code': departure_airport,
      'arrival.airport_code': arrival_airport,
      'departure.date': {
        $gte: new Date(departure_date),
        $lt: new Date(new Date(departure_date).getTime() + 24 * 60 * 60 * 1000)
      }
    });
    
    const results = {
      outbound_flights: outboundFlights.map(flight => ({
        id: flight._id,
        airline: flight.airline,
        flight_number: flight.flight_number,
        departure: flight.departure,
        arrival: flight.arrival,
        duration: flight.duration,
        formatted_duration: flight.formatted_duration,
        price: flight.price,
        available_seats: flight.available_seats,
        stops: flight.stops,
        layovers: flight.layovers,
        amenities: flight.amenities,
        baggage: flight.baggage,
        cancellation_policy: flight.cancellation_policy
      })),
      return_flights: returnFlights.map(flight => ({
        id: flight._id,
        airline: flight.airline,
        flight_number: flight.flight_number,
        departure: flight.departure,
        arrival: flight.arrival,
        duration: flight.duration,
        formatted_duration: flight.formatted_duration,
        price: flight.price,
        available_seats: flight.available_seats,
        stops: flight.stops,
        layovers: flight.layovers,
        amenities: flight.amenities,
        baggage: flight.baggage,
        cancellation_policy: flight.cancellation_policy
      })),
      search_params: {
        departure_airport,
        arrival_airport,
        departure_date,
        return_date,
        passengers: parseInt(passengers),
        travel_class
      },
      pagination: {
        total: totalOutbound,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < totalOutbound
      }
    };
    
    // Cache results for 15 minutes (flight prices change frequently)
    await cache.set(cacheKey, results, 900);
    
    res.json(results);
    
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/flights/:id
 * Get flight details
 */
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const flight = await Flight.findById(id);
    
    if (!flight) {
      return res.status(404).json({
        error: true,
        message: 'Flight not found'
      });
    }
    
    res.json({
      id: flight._id,
      airline: flight.airline,
      flight_number: flight.flight_number,
      departure: flight.departure,
      arrival: flight.arrival,
      duration: flight.duration,
      formatted_duration: flight.formatted_duration,
      aircraft: flight.aircraft,
      price: flight.price,
      available_seats: flight.available_seats,
      stops: flight.stops,
      layovers: flight.layovers,
      amenities: flight.amenities,
      baggage: flight.baggage,
      cancellation_policy: flight.cancellation_policy
    });
    
  } catch (error) {
    next(error);
  }
});

module.exports = router;
