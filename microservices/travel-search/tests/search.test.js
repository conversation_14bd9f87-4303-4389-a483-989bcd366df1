/**
 * Test cases for travel search endpoints
 */

const request = require('supertest');
const app = require('../src/index');

describe('Travel Search API', () => {
  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('service', 'travel-search');
    });
  });

  describe('GET /api/search/destinations', () => {
    it('should return destinations list', async () => {
      const response = await request(app)
        .get('/api/search/destinations')
        .expect(200);

      expect(response.body).toHaveProperty('destinations');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.destinations)).toBe(true);
    });

    it('should filter destinations by query', async () => {
      const response = await request(app)
        .get('/api/search/destinations?q=paris')
        .expect(200);

      expect(response.body).toHaveProperty('destinations');
      expect(Array.isArray(response.body.destinations)).toBe(true);
    });

    it('should validate query parameters', async () => {
      const response = await request(app)
        .get('/api/search/destinations?q=a')
        .expect(400);

      expect(response.body).toHaveProperty('error', true);
      expect(response.body).toHaveProperty('message', 'Validation failed');
    });
  });

  describe('GET /api/search/autocomplete', () => {
    it('should return autocomplete suggestions', async () => {
      const response = await request(app)
        .get('/api/search/autocomplete?q=par')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should require minimum query length', async () => {
      const response = await request(app)
        .get('/api/search/autocomplete?q=p')
        .expect(400);

      expect(response.body).toHaveProperty('error', true);
    });
  });

  describe('GET /api/search/popular', () => {
    it('should return popular destinations', async () => {
      const response = await request(app)
        .get('/api/search/popular')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should respect limit parameter', async () => {
      const response = await request(app)
        .get('/api/search/popular?limit=5')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(5);
    });
  });

  describe('GET /api/flights/search', () => {
    it('should search flights with valid parameters', async () => {
      const response = await request(app)
        .get('/api/flights/search?departure_airport=JFK&arrival_airport=LAX&departure_date=2024-12-01')
        .expect(200);

      expect(response.body).toHaveProperty('outbound_flights');
      expect(response.body).toHaveProperty('search_params');
      expect(response.body).toHaveProperty('pagination');
    });

    it('should validate required parameters', async () => {
      const response = await request(app)
        .get('/api/flights/search?departure_airport=JFK')
        .expect(400);

      expect(response.body).toHaveProperty('error', true);
    });

    it('should validate airport codes', async () => {
      const response = await request(app)
        .get('/api/flights/search?departure_airport=INVALID&arrival_airport=LAX&departure_date=2024-12-01')
        .expect(400);

      expect(response.body).toHaveProperty('error', true);
    });
  });

  describe('GET /api/hotels/search', () => {
    it('should search hotels with valid parameters', async () => {
      const response = await request(app)
        .get('/api/hotels/search?destination=paris&check_in=2024-12-01&check_out=2024-12-05')
        .expect(200);

      expect(response.body).toHaveProperty('hotels');
      expect(response.body).toHaveProperty('search_params');
      expect(response.body).toHaveProperty('pagination');
    });

    it('should validate required parameters', async () => {
      const response = await request(app)
        .get('/api/hotels/search?destination=paris')
        .expect(400);

      expect(response.body).toHaveProperty('error', true);
    });
  });

  describe('GET /api/packages/search', () => {
    it('should search travel packages', async () => {
      const response = await request(app)
        .get('/api/packages/search')
        .expect(200);

      expect(response.body).toHaveProperty('packages');
      expect(response.body).toHaveProperty('pagination');
    });

    it('should filter packages by destination', async () => {
      const response = await request(app)
        .get('/api/packages/search?destination=europe')
        .expect(200);

      expect(response.body).toHaveProperty('packages');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);

      expect(response.body).toHaveProperty('error', true);
    });
  });
});

module.exports = app;
