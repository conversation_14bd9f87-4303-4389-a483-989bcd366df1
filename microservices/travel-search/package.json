{"name": "vtravels-travel-search", "version": "1.0.0", "description": "Travel search microservice for VTravels", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["travel", "search", "microservice", "nodejs"], "author": "VTravels Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}}