/**
 * VTravels Notifications Microservice
 * Handles email, SMS, and push notifications
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const http = require('http');
const socketIo = require('socket.io');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3003;

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'notifications',
    version: '1.0.0'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'VTravels Notifications Microservice',
    version: '1.0.0'
  });
});

// Send notification endpoint
app.post('/api/notifications/send', (req, res) => {
  const { userId, type, title, message, data } = req.body;
  
  // Mock notification sending
  console.log(`Sending ${type} notification to user ${userId}: ${title}`);
  
  // Emit real-time notification via Socket.IO
  io.to(`user_${userId}`).emit('notification', {
    id: Date.now(),
    type,
    title,
    message,
    data,
    timestamp: new Date().toISOString()
  });
  
  res.json({
    success: true,
    message: 'Notification sent successfully'
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);
  
  socket.on('join', (userId) => {
    socket.join(`user_${userId}`);
    console.log(`User ${userId} joined notification room`);
  });
  
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

server.listen(PORT, () => {
  console.log(`Notifications service running on port ${PORT}`);
});
