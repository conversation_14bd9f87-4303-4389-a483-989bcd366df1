{"name": "vtravels-notifications", "version": "1.0.0", "description": "Notifications microservice for VTravels", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "winston": "^3.11.0", "dotenv": "^16.3.1", "nodemailer": "^6.9.7", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}