/**
 * VTravels Reviews Microservice
 * Handles user reviews and ratings for destinations, hotels, and services
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const { body, param, query, validationResult } = require('express-validator');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json());

// Mock reviews data
let reviews = [
  {
    id: '1',
    user_id: 1,
    entity_type: 'destination',
    entity_id: 'paris',
    rating: 5,
    title: 'Amazing city with so much to see!',
    comment: 'Paris exceeded all my expectations. The architecture, food, and culture are incredible.',
    helpful_count: 15,
    photos: [],
    travel_date: '2023-09-15',
    created_at: '2023-09-20T10:00:00Z'
  },
  {
    id: '2',
    user_id: 2,
    entity_type: 'hotel',
    entity_id: 'hotel_123',
    rating: 4,
    title: 'Excellent service and location',
    comment: 'The hotel staff was incredibly helpful and the location is perfect for exploring the city.',
    helpful_count: 8,
    photos: [],
    travel_date: '2023-08-10',
    created_at: '2023-08-15T14:30:00Z'
  }
];

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: true,
      message: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'reviews',
    version: '1.0.0'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'VTravels Reviews Microservice',
    version: '1.0.0'
  });
});

/**
 * GET /api/reviews
 * Get reviews with filtering options
 */
app.get('/api/reviews', [
  query('entity_type').optional().isIn(['destination', 'hotel', 'flight', 'package']),
  query('entity_id').optional().isLength({ min: 1 }),
  query('user_id').optional().isInt({ min: 1 }),
  query('min_rating').optional().isInt({ min: 1, max: 5 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('offset').optional().isInt({ min: 0 })
], handleValidationErrors, (req, res) => {
  const {
    entity_type,
    entity_id,
    user_id,
    min_rating,
    limit = 20,
    offset = 0
  } = req.query;

  let filteredReviews = reviews;

  // Apply filters
  if (entity_type) {
    filteredReviews = filteredReviews.filter(r => r.entity_type === entity_type);
  }
  if (entity_id) {
    filteredReviews = filteredReviews.filter(r => r.entity_id === entity_id);
  }
  if (user_id) {
    filteredReviews = filteredReviews.filter(r => r.user_id === parseInt(user_id));
  }
  if (min_rating) {
    filteredReviews = filteredReviews.filter(r => r.rating >= parseInt(min_rating));
  }

  // Apply pagination
  const startIndex = parseInt(offset);
  const endIndex = startIndex + parseInt(limit);
  const paginatedReviews = filteredReviews.slice(startIndex, endIndex);

  res.json({
    reviews: paginatedReviews,
    pagination: {
      total: filteredReviews.length,
      limit: parseInt(limit),
      offset: parseInt(offset),
      hasMore: endIndex < filteredReviews.length
    }
  });
});

/**
 * GET /api/reviews/:id
 * Get specific review by ID
 */
app.get('/api/reviews/:id', [
  param('id').isLength({ min: 1 })
], handleValidationErrors, (req, res) => {
  const { id } = req.params;
  const review = reviews.find(r => r.id === id);

  if (!review) {
    return res.status(404).json({
      error: true,
      message: 'Review not found'
    });
  }

  res.json(review);
});

/**
 * POST /api/reviews
 * Create a new review
 */
app.post('/api/reviews', [
  body('user_id').isInt({ min: 1 }),
  body('entity_type').isIn(['destination', 'hotel', 'flight', 'package']),
  body('entity_id').isLength({ min: 1 }),
  body('rating').isInt({ min: 1, max: 5 }),
  body('title').isLength({ min: 5, max: 200 }),
  body('comment').isLength({ min: 10, max: 2000 }),
  body('travel_date').optional().isISO8601()
], handleValidationErrors, (req, res) => {
  const {
    user_id,
    entity_type,
    entity_id,
    rating,
    title,
    comment,
    travel_date,
    photos = []
  } = req.body;

  // Check if user already reviewed this entity
  const existingReview = reviews.find(r => 
    r.user_id === user_id && 
    r.entity_type === entity_type && 
    r.entity_id === entity_id
  );

  if (existingReview) {
    return res.status(400).json({
      error: true,
      message: 'You have already reviewed this item'
    });
  }

  const newReview = {
    id: (reviews.length + 1).toString(),
    user_id,
    entity_type,
    entity_id,
    rating,
    title,
    comment,
    helpful_count: 0,
    photos,
    travel_date,
    created_at: new Date().toISOString()
  };

  reviews.push(newReview);

  res.status(201).json({
    message: 'Review created successfully',
    review: newReview
  });
});

/**
 * PUT /api/reviews/:id/helpful
 * Mark review as helpful
 */
app.put('/api/reviews/:id/helpful', [
  param('id').isLength({ min: 1 })
], handleValidationErrors, (req, res) => {
  const { id } = req.params;
  const review = reviews.find(r => r.id === id);

  if (!review) {
    return res.status(404).json({
      error: true,
      message: 'Review not found'
    });
  }

  review.helpful_count += 1;

  res.json({
    message: 'Review marked as helpful',
    helpful_count: review.helpful_count
  });
});

/**
 * GET /api/reviews/stats/:entity_type/:entity_id
 * Get review statistics for an entity
 */
app.get('/api/reviews/stats/:entity_type/:entity_id', [
  param('entity_type').isIn(['destination', 'hotel', 'flight', 'package']),
  param('entity_id').isLength({ min: 1 })
], handleValidationErrors, (req, res) => {
  const { entity_type, entity_id } = req.params;
  
  const entityReviews = reviews.filter(r => 
    r.entity_type === entity_type && r.entity_id === entity_id
  );

  if (entityReviews.length === 0) {
    return res.json({
      total_reviews: 0,
      average_rating: 0,
      rating_distribution: {
        5: 0, 4: 0, 3: 0, 2: 0, 1: 0
      }
    });
  }

  const totalRating = entityReviews.reduce((sum, r) => sum + r.rating, 0);
  const averageRating = (totalRating / entityReviews.length).toFixed(1);

  const ratingDistribution = {
    5: entityReviews.filter(r => r.rating === 5).length,
    4: entityReviews.filter(r => r.rating === 4).length,
    3: entityReviews.filter(r => r.rating === 3).length,
    2: entityReviews.filter(r => r.rating === 2).length,
    1: entityReviews.filter(r => r.rating === 1).length
  };

  res.json({
    total_reviews: entityReviews.length,
    average_rating: parseFloat(averageRating),
    rating_distribution: ratingDistribution
  });
});

app.listen(PORT, () => {
  console.log(`Reviews service running on port ${PORT}`);
});
