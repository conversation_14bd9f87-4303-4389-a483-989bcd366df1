{"name": "vtravels-frontend", "version": "1.0.0", "description": "VTravels React Frontend Application", "private": true, "dependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "@mui/material": "^5.15.2", "@mui/icons-material": "^5.15.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.14.2", "axios": "^1.6.2", "date-fns": "^3.0.6", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-toastify": "^9.1.3", "lodash": "^4.17.21", "@types/lodash": "^4.14.202"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.8", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1"}, "proxy": "http://localhost:8080"}