import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Paper,
  useTheme,
} from '@mui/material';
import {
  Flight,
  Hotel,
  CardTravel,
  Star,
  TrendingUp,
  Security,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();

  const features = [
    {
      icon: <Flight sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      title: 'Flight Booking',
      description: 'Find and book flights to destinations worldwide with competitive prices.',
    },
    {
      icon: <Hotel sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      title: 'Hotel Reservations',
      description: 'Discover and book accommodations from budget to luxury hotels.',
    },
    {
      icon: <CardTravel sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      title: 'Travel Packages',
      description: 'Complete travel packages with flights, hotels, and activities included.',
    },
  ];

  const benefits = [
    {
      icon: <Star sx={{ color: theme.palette.secondary.main }} />,
      title: 'Best Prices',
      description: 'We guarantee the best prices for your travel bookings.',
    },
    {
      icon: <Security sx={{ color: theme.palette.secondary.main }} />,
      title: 'Secure Booking',
      description: 'Your personal and payment information is always protected.',
    },
    {
      icon: <TrendingUp sx={{ color: theme.palette.secondary.main }} />,
      title: '24/7 Support',
      description: 'Our customer support team is available around the clock.',
    },
  ];

  const popularDestinations = [
    {
      name: 'Paris, France',
      image: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=400',
      description: 'The City of Light awaits with its iconic landmarks and culture.',
    },
    {
      name: 'Tokyo, Japan',
      image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=400',
      description: 'Experience the perfect blend of tradition and modernity.',
    },
    {
      name: 'New York, USA',
      image: 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?w=400',
      description: 'The city that never sleeps offers endless possibilities.',
    },
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
          color: 'white',
          py: 12,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h2" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
            Your Journey Starts Here
          </Typography>
          <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
            Discover amazing destinations, book flights and hotels, and create unforgettable memories
          </Typography>
          <Button
            variant="contained"
            size="large"
            onClick={() => navigate('/search')}
            sx={{
              bgcolor: 'white',
              color: 'primary.main',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              '&:hover': {
                bgcolor: 'grey.100',
              },
            }}
          >
            Start Planning Your Trip
          </Button>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
          Why Choose VTravels?
        </Typography>
        <Typography
          variant="h6"
          textAlign="center"
          color="text.secondary"
          sx={{ mb: 6 }}
        >
          We make travel planning simple, secure, and affordable
        </Typography>

        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Paper
                elevation={2}
                sx={{
                  p: 4,
                  textAlign: 'center',
                  height: '100%',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                  },
                }}
              >
                <Box sx={{ mb: 2 }}>{feature.icon}</Box>
                <Typography variant="h5" gutterBottom>
                  {feature.title}
                </Typography>
                <Typography color="text.secondary">
                  {feature.description}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Popular Destinations */}
      <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
            Popular Destinations
          </Typography>
          <Typography
            variant="h6"
            textAlign="center"
            color="text.secondary"
            sx={{ mb: 6 }}
          >
            Explore the world's most amazing places
          </Typography>

          <Grid container spacing={4}>
            {popularDestinations.map((destination, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                    },
                  }}
                >
                  <CardMedia
                    component="img"
                    height="200"
                    image={destination.image}
                    alt={destination.name}
                  />
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {destination.name}
                    </Typography>
                    <Typography color="text.secondary">
                      {destination.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Benefits Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h3" component="h2" gutterBottom>
              Travel with Confidence
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              Join millions of travelers who trust VTravels for their journey
            </Typography>

            {benefits.map((benefit, index) => (
              <Box key={index} sx={{ display: 'flex', mb: 3 }}>
                <Box sx={{ mr: 2, mt: 0.5 }}>{benefit.icon}</Box>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    {benefit.title}
                  </Typography>
                  <Typography color="text.secondary">
                    {benefit.description}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Grid>
          <Grid item xs={12} md={6}>
            <Box
              component="img"
              src="https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=600"
              alt="Travel"
              sx={{
                width: '100%',
                height: 'auto',
                borderRadius: 2,
              }}
            />
          </Grid>
        </Grid>
      </Container>

      {/* CTA Section */}
      <Box
        sx={{
          bgcolor: 'primary.main',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" component="h2" gutterBottom>
            Ready to Start Your Adventure?
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
            Join thousands of happy travelers and book your next trip today
          </Typography>
          <Button
            variant="contained"
            size="large"
            onClick={() => navigate('/register')}
            sx={{
              bgcolor: 'white',
              color: 'primary.main',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              mr: 2,
              '&:hover': {
                bgcolor: 'grey.100',
              },
            }}
          >
            Sign Up Now
          </Button>
          <Button
            variant="outlined"
            size="large"
            onClick={() => navigate('/search')}
            sx={{
              borderColor: 'white',
              color: 'white',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              '&:hover': {
                borderColor: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            Browse Destinations
          </Button>
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
