import React from 'react';
import { Container, Typography, Box, Paper, Grid } from '@mui/material';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';

const DashboardPage: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom>
        Welcome back, {user?.first_name}!
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper elevation={2} sx={{ p: 4 }}>
            <Typography variant="h5" gutterBottom>
              Your Travel Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Dashboard functionality will be implemented here including:
            </Typography>
            <ul style={{ marginTop: 20 }}>
              <li>Recent bookings overview</li>
              <li>Upcoming trips</li>
              <li>Travel recommendations</li>
              <li>Quick booking actions</li>
              <li>Travel statistics</li>
            </ul>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Typography variant="body2">
              • Search flights<br/>
              • Find hotels<br/>
              • Browse packages<br/>
              • View bookings
            </Typography>
          </Paper>
          
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Account Status
            </Typography>
            <Typography variant="body2">
              Email: {user?.email}<br/>
              Status: {user?.is_verified ? 'Verified' : 'Pending verification'}<br/>
              Member since: {user?.created_at ? new Date(user.created_at).getFullYear() : 'N/A'}
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DashboardPage;
