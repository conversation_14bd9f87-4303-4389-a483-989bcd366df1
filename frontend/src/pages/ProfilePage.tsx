import React from 'react';
import { Container, Typography, Paper } from '@mui/material';

const ProfilePage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom>
        My Profile
      </Typography>
      
      <Paper elevation={2} sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Profile management will be implemented here
        </Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>
          This page will include:
        </Typography>
        <ul style={{ textAlign: 'left', maxWidth: 400, margin: '20px auto' }}>
          <li>Personal information editing</li>
          <li>Password change</li>
          <li>Travel preferences</li>
          <li>Notification settings</li>
          <li>Account security</li>
        </ul>
      </Paper>
    </Container>
  );
};

export default ProfilePage;
