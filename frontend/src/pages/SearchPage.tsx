import React from 'react';
import { Container, Typography, Box, Paper } from '@mui/material';

const SearchPage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={2} sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h3" gutterBottom>
          Search Travel Options
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Search functionality will be implemented here
        </Typography>
        <Box sx={{ mt: 4 }}>
          <Typography variant="body1">
            This page will include:
          </Typography>
          <ul style={{ textAlign: 'left', maxWidth: 400, margin: '20px auto' }}>
            <li>Flight search with filters</li>
            <li>Hotel search with location and dates</li>
            <li>Travel package browsing</li>
            <li>Advanced filtering options</li>
            <li>Search results with sorting</li>
          </ul>
        </Box>
      </Paper>
    </Container>
  );
};

export default SearchPage;
