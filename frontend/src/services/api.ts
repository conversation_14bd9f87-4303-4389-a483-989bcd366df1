import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: { email: string; password: string }) =>
    api.post('/api/v1/auth/login', credentials),
  
  register: (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    phone?: string;
  }) => api.post('/api/v1/auth/register', userData),
  
  logout: () => api.post('/api/v1/auth/logout'),
  
  getCurrentUser: () => api.get('/api/v1/auth/me'),
  
  refreshToken: (refreshToken: string) =>
    api.post('/api/v1/auth/refresh', { refresh_token: refreshToken }),
};

// Search API
export const searchAPI = {
  searchDestinations: (params: {
    q?: string;
    country?: string;
    limit?: number;
    offset?: number;
  }) => api.get('/api/search/destinations', { params }),
  
  autocomplete: (params: { q: string; limit?: number }) =>
    api.get('/api/search/autocomplete', { params }),
  
  getPopularDestinations: (params: { limit?: number }) =>
    api.get('/api/search/popular', { params }),
  
  searchFlights: (params: {
    departure_airport: string;
    arrival_airport: string;
    departure_date: string;
    return_date?: string;
    passengers?: number;
    travel_class?: string;
    max_price?: number;
    max_stops?: number;
    airline?: string;
    limit?: number;
    offset?: number;
  }) => api.get('/api/search/flights/search', { params }),
  
  getFlightDetails: (id: string) => api.get(`/api/search/flights/${id}`),
  
  searchHotels: (params: {
    destination?: string;
    check_in: string;
    check_out: string;
    guests?: number;
    rooms?: number;
    min_price?: number;
    max_price?: number;
    star_rating?: number;
    amenities?: string;
    limit?: number;
    offset?: number;
  }) => api.get('/api/search/hotels/search', { params }),
  
  getHotelDetails: (id: string) => api.get(`/api/search/hotels/${id}`),
  
  searchPackages: (params: {
    destination?: string;
    duration?: number;
    budget?: number;
    limit?: number;
    offset?: number;
  }) => api.get('/api/search/packages/search', { params }),
  
  getPackageDetails: (id: string) => api.get(`/api/search/packages/${id}`),
};

// Booking API
export const bookingAPI = {
  getBookings: (params: { skip?: number; limit?: number }) =>
    api.get('/api/v1/bookings', { params }),
  
  getBookingDetails: (id: number) => api.get(`/api/v1/bookings/${id}`),
  
  cancelBooking: (id: number) => api.put(`/api/v1/bookings/${id}/cancel`),
  
  createBooking: (bookingData: any) => api.post('/api/v1/bookings', bookingData),
};

// User API
export const userAPI = {
  getProfile: () => api.get('/api/v1/users/profile'),
  
  updateProfile: (profileData: {
    first_name?: string;
    last_name?: string;
    phone?: string;
  }) => api.put('/api/v1/users/profile', profileData),
};

// Reviews API
export const reviewsAPI = {
  getReviews: (params: {
    entity_type?: string;
    entity_id?: string;
    user_id?: number;
    min_rating?: number;
    limit?: number;
    offset?: number;
  }) => api.get('/api/reviews', { params }),
  
  getReviewDetails: (id: string) => api.get(`/api/reviews/${id}`),
  
  createReview: (reviewData: {
    user_id: number;
    entity_type: string;
    entity_id: string;
    rating: number;
    title: string;
    comment: string;
    travel_date?: string;
    photos?: string[];
  }) => api.post('/api/reviews', reviewData),
  
  markReviewHelpful: (id: string) => api.put(`/api/reviews/${id}/helpful`),
  
  getReviewStats: (entityType: string, entityId: string) =>
    api.get(`/api/reviews/stats/${entityType}/${entityId}`),
};

// Recommendations API
export const recommendationsAPI = {
  getUserRecommendations: (userId: number) =>
    api.get(`/api/recommendations/${userId}`),
};

// Notifications API
export const notificationsAPI = {
  sendNotification: (notificationData: {
    userId: number;
    type: string;
    title: string;
    message: string;
    data?: any;
  }) => api.post('/api/notifications/send', notificationData),
};

export default api;
