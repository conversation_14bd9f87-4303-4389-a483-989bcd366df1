import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface SearchFilters {
  destination?: string;
  checkIn?: string;
  checkOut?: string;
  guests?: number;
  rooms?: number;
  departureAirport?: string;
  arrivalAirport?: string;
  departureDate?: string;
  returnDate?: string;
  passengers?: number;
  travelClass?: 'economy' | 'premium_economy' | 'business' | 'first';
}

export interface SearchState {
  filters: SearchFilters;
  searchType: 'hotels' | 'flights' | 'packages';
  isSearching: boolean;
  results: any[];
  totalResults: number;
  currentPage: number;
  hasMore: boolean;
}

const initialState: SearchState = {
  filters: {
    guests: 2,
    rooms: 1,
    passengers: 1,
    travelClass: 'economy',
  },
  searchType: 'hotels',
  isSearching: false,
  results: [],
  totalResults: 0,
  currentPage: 1,
  hasMore: false,
};

const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setSearchType: (state, action: PayloadAction<'hotels' | 'flights' | 'packages'>) => {
      state.searchType = action.payload;
      state.results = [];
      state.totalResults = 0;
      state.currentPage = 1;
      state.hasMore = false;
    },
    setFilters: (state, action: PayloadAction<Partial<SearchFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSearching: (state, action: PayloadAction<boolean>) => {
      state.isSearching = action.payload;
    },
    setResults: (state, action: PayloadAction<{
      results: any[];
      totalResults: number;
      currentPage: number;
      hasMore: boolean;
    }>) => {
      state.results = action.payload.results;
      state.totalResults = action.payload.totalResults;
      state.currentPage = action.payload.currentPage;
      state.hasMore = action.payload.hasMore;
    },
    appendResults: (state, action: PayloadAction<{
      results: any[];
      currentPage: number;
      hasMore: boolean;
    }>) => {
      state.results = [...state.results, ...action.payload.results];
      state.currentPage = action.payload.currentPage;
      state.hasMore = action.payload.hasMore;
    },
    clearResults: (state) => {
      state.results = [];
      state.totalResults = 0;
      state.currentPage = 1;
      state.hasMore = false;
    },
  },
});

export const {
  setSearchType,
  setFilters,
  setSearching,
  setResults,
  appendResults,
  clearResults,
} = searchSlice.actions;

export default searchSlice.reducer;
