version: '3.8'

services:
  # Databases
  mysql:
    image: mysql:8.0
    container_name: vtravels_mysql_dev
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: vtravels
      MYSQL_USER: vtravels_user
      MYSQL_PASSWORD: vtravels_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./databases/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - vtravels_dev_network

  mongodb:
    image: mongo:6.0
    container_name: vtravels_mongodb_dev
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: adminpassword
      MONGO_INITDB_DATABASE: vtravels
    ports:
      - "27017:27017"
    volumes:
      - mongodb_dev_data:/data/db
      - ./databases/mongodb/init.js:/docker-entrypoint-initdb.d/init.js
    networks:
      - vtravels_dev_network

  redis:
    image: redis:7.0-alpine
    container_name: vtravels_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - vtravels_dev_network

  # Python Backend (Development mode)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: vtravels_backend_dev
    environment:
      - DATABASE_URL=mysql+pymysql://vtravels_user:vtravels_password@mysql:3306/vtravels
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev-super-secret-jwt-key
      - DEBUG=true
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - mongodb
      - redis
    volumes:
      - ./backend:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - vtravels_dev_network

  # Node.js Microservices (Development mode)
  travel-search:
    build:
      context: ./microservices/travel-search
      dockerfile: Dockerfile
    container_name: vtravels_travel_search_dev
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3001
      - NODE_ENV=development
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/travel-search:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - vtravels_dev_network

  recommendations:
    build:
      context: ./microservices/recommendations
      dockerfile: Dockerfile
    container_name: vtravels_recommendations_dev
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3002
      - NODE_ENV=development
    ports:
      - "3002:3002"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/recommendations:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - vtravels_dev_network

  notifications:
    build:
      context: ./microservices/notifications
      dockerfile: Dockerfile
    container_name: vtravels_notifications_dev
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3003
      - NODE_ENV=development
    ports:
      - "3003:3003"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/notifications:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - vtravels_dev_network

  reviews:
    build:
      context: ./microservices/reviews
      dockerfile: Dockerfile
    container_name: vtravels_reviews_dev
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3004
      - NODE_ENV=development
    ports:
      - "3004:3004"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/reviews:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - vtravels_dev_network

  # API Gateway (Development mode)
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: vtravels_api_gateway_dev
    environment:
      - BACKEND_URL=http://backend:8000
      - TRAVEL_SEARCH_URL=http://travel-search:3001
      - RECOMMENDATIONS_URL=http://recommendations:3002
      - NOTIFICATIONS_URL=http://notifications:3003
      - REVIEWS_URL=http://reviews:3004
      - NODE_ENV=development
    ports:
      - "8080:8080"
    depends_on:
      - backend
      - travel-search
      - recommendations
      - notifications
      - reviews
    volumes:
      - ./api-gateway:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - vtravels_dev_network

  # Frontend (Development mode)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: vtravels_frontend_dev
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
    depends_on:
      - api-gateway
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm start
    networks:
      - vtravels_dev_network

volumes:
  mysql_dev_data:
  mongodb_dev_data:
  redis_dev_data:

networks:
  vtravels_dev_network:
    driver: bridge
