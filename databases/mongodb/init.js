// VTravels MongoDB Database Initialization
// This script initializes the MongoDB database with collections for travel data, reviews, and search

// Switch to vtravels database
db = db.getSiblingDB('vtravels');

// Create collections and indexes

// Destinations collection - stores travel destinations information
db.createCollection('destinations');
db.destinations.createIndex({ "name": "text", "description": "text", "city": "text", "country": "text" });
db.destinations.createIndex({ "location": "2dsphere" });
db.destinations.createIndex({ "country": 1, "city": 1 });
db.destinations.createIndex({ "rating": -1 });
db.destinations.createIndex({ "popular": -1 });

// Hotels collection - stores hotel information
db.createCollection('hotels');
db.hotels.createIndex({ "name": "text", "description": "text", "address.city": "text" });
db.hotels.createIndex({ "location": "2dsphere" });
db.hotels.createIndex({ "destination_id": 1 });
db.hotels.createIndex({ "rating": -1 });
db.hotels.createIndex({ "price_range.min": 1, "price_range.max": 1 });
db.hotels.createIndex({ "amenities": 1 });

// Flights collection - stores flight information
db.createCollection('flights');
db.flights.createIndex({ "airline": 1 });
db.flights.createIndex({ "departure.airport_code": 1, "arrival.airport_code": 1 });
db.flights.createIndex({ "departure.date": 1 });
db.flights.createIndex({ "price": 1 });
db.flights.createIndex({ "duration": 1 });

// Reviews collection - stores user reviews and ratings
db.createCollection('reviews');
db.reviews.createIndex({ "user_id": 1 });
db.reviews.createIndex({ "entity_type": 1, "entity_id": 1 });
db.reviews.createIndex({ "rating": -1 });
db.reviews.createIndex({ "created_at": -1 });
db.reviews.createIndex({ "helpful_count": -1 });

// Search history collection - stores user search patterns
db.createCollection('search_history');
db.search_history.createIndex({ "user_id": 1 });
db.search_history.createIndex({ "search_type": 1 });
db.search_history.createIndex({ "created_at": -1 });

// Travel packages collection - stores curated travel packages
db.createCollection('travel_packages');
db.travel_packages.createIndex({ "name": "text", "description": "text" });
db.travel_packages.createIndex({ "destinations": 1 });
db.travel_packages.createIndex({ "price": 1 });
db.travel_packages.createIndex({ "duration": 1 });
db.travel_packages.createIndex({ "rating": -1 });

// User preferences collection - stores user travel preferences for recommendations
db.createCollection('user_travel_preferences');
db.user_travel_preferences.createIndex({ "user_id": 1 }, { unique: true });
db.user_travel_preferences.createIndex({ "preferred_destinations": 1 });
db.user_travel_preferences.createIndex({ "budget_range.min": 1, "budget_range.max": 1 });

// Insert sample data

// Sample destinations
db.destinations.insertMany([
  {
    _id: ObjectId(),
    name: "Paris",
    description: "The City of Light, famous for its art, fashion, gastronomy, and culture.",
    country: "France",
    city: "Paris",
    location: {
      type: "Point",
      coordinates: [2.3522, 48.8566] // [longitude, latitude]
    },
    rating: 4.7,
    popular: true,
    image_urls: [
      "https://example.com/paris1.jpg",
      "https://example.com/paris2.jpg"
    ],
    attractions: ["Eiffel Tower", "Louvre Museum", "Notre-Dame Cathedral", "Arc de Triomphe"],
    best_time_to_visit: ["April", "May", "September", "October"],
    average_temperature: {
      summer: "25°C",
      winter: "7°C"
    },
    currency: "EUR",
    language: "French",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    _id: ObjectId(),
    name: "Tokyo",
    description: "A bustling metropolis blending traditional culture with cutting-edge technology.",
    country: "Japan",
    city: "Tokyo",
    location: {
      type: "Point",
      coordinates: [139.6917, 35.6895]
    },
    rating: 4.8,
    popular: true,
    image_urls: [
      "https://example.com/tokyo1.jpg",
      "https://example.com/tokyo2.jpg"
    ],
    attractions: ["Tokyo Tower", "Senso-ji Temple", "Shibuya Crossing", "Imperial Palace"],
    best_time_to_visit: ["March", "April", "May", "October", "November"],
    average_temperature: {
      summer: "30°C",
      winter: "10°C"
    },
    currency: "JPY",
    language: "Japanese",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    _id: ObjectId(),
    name: "New York City",
    description: "The Big Apple - a vibrant city that never sleeps.",
    country: "United States",
    city: "New York",
    location: {
      type: "Point",
      coordinates: [-74.0060, 40.7128]
    },
    rating: 4.6,
    popular: true,
    image_urls: [
      "https://example.com/nyc1.jpg",
      "https://example.com/nyc2.jpg"
    ],
    attractions: ["Statue of Liberty", "Central Park", "Times Square", "Empire State Building"],
    best_time_to_visit: ["April", "May", "June", "September", "October", "November"],
    average_temperature: {
      summer: "28°C",
      winter: "4°C"
    },
    currency: "USD",
    language: "English",
    created_at: new Date(),
    updated_at: new Date()
  }
]);

// Sample hotels
db.hotels.insertMany([
  {
    _id: ObjectId(),
    name: "Hotel de Crillon",
    description: "Luxury hotel in the heart of Paris with stunning views of Place de la Concorde.",
    destination_id: "paris",
    address: {
      street: "10 Place de la Concorde",
      city: "Paris",
      country: "France",
      postal_code: "75008"
    },
    location: {
      type: "Point",
      coordinates: [2.3213, 48.8656]
    },
    rating: 4.9,
    star_rating: 5,
    price_range: {
      min: 800,
      max: 2000,
      currency: "EUR"
    },
    amenities: ["WiFi", "Spa", "Restaurant", "Bar", "Gym", "Concierge", "Room Service"],
    room_types: [
      {
        type: "Deluxe Room",
        price: 800,
        capacity: 2,
        amenities: ["King Bed", "City View", "Marble Bathroom"]
      },
      {
        type: "Suite",
        price: 1500,
        capacity: 4,
        amenities: ["Separate Living Room", "Balcony", "Butler Service"]
      }
    ],
    images: [
      "https://example.com/hotel-crillon1.jpg",
      "https://example.com/hotel-crillon2.jpg"
    ],
    policies: {
      check_in: "15:00",
      check_out: "12:00",
      cancellation: "Free cancellation up to 24 hours before check-in"
    },
    created_at: new Date(),
    updated_at: new Date()
  }
]);

// Sample reviews
db.reviews.insertMany([
  {
    _id: ObjectId(),
    user_id: 1,
    entity_type: "destination",
    entity_id: "paris",
    rating: 5,
    title: "Amazing city with so much to see!",
    comment: "Paris exceeded all my expectations. The architecture, food, and culture are incredible. Definitely coming back!",
    helpful_count: 15,
    photos: ["https://example.com/review-photo1.jpg"],
    travel_date: new Date("2023-09-15"),
    created_at: new Date("2023-09-20"),
    updated_at: new Date("2023-09-20")
  },
  {
    _id: ObjectId(),
    user_id: 2,
    entity_type: "hotel",
    entity_id: ObjectId(),
    rating: 4,
    title: "Excellent service and location",
    comment: "The hotel staff was incredibly helpful and the location is perfect for exploring the city. Room was clean and comfortable.",
    helpful_count: 8,
    photos: [],
    travel_date: new Date("2023-08-10"),
    created_at: new Date("2023-08-15"),
    updated_at: new Date("2023-08-15")
  }
]);

// Sample travel packages
db.travel_packages.insertMany([
  {
    _id: ObjectId(),
    name: "European Grand Tour",
    description: "Visit 5 amazing European cities in 14 days with guided tours and luxury accommodations.",
    destinations: ["paris", "rome", "barcelona", "amsterdam", "prague"],
    duration: 14,
    price: 3500,
    currency: "USD",
    rating: 4.8,
    includes: [
      "Round-trip flights",
      "4-star hotel accommodations",
      "Daily breakfast",
      "Guided city tours",
      "Airport transfers"
    ],
    excludes: [
      "Lunch and dinner",
      "Personal expenses",
      "Travel insurance",
      "Visa fees"
    ],
    itinerary: [
      {
        day: 1,
        city: "Paris",
        activities: ["Arrival and hotel check-in", "Welcome dinner"]
      },
      {
        day: 2,
        city: "Paris",
        activities: ["Eiffel Tower visit", "Seine River cruise", "Louvre Museum"]
      }
    ],
    available_dates: [
      new Date("2024-05-01"),
      new Date("2024-06-01"),
      new Date("2024-09-01")
    ],
    max_participants: 20,
    difficulty_level: "Easy",
    age_restriction: "18+",
    created_at: new Date(),
    updated_at: new Date()
  }
]);

print("MongoDB initialization completed successfully!");
print("Collections created: destinations, hotels, flights, reviews, search_history, travel_packages, user_travel_preferences");
print("Sample data inserted for testing purposes.");
