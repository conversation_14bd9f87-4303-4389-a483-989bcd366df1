# VTravels Development Makefile

.PHONY: help dev prod build test clean logs stop restart

# Default target
help:
	@echo "VTravels Development Commands"
	@echo "============================="
	@echo ""
	@echo "Development:"
	@echo "  make dev          - Start development environment"
	@echo "  make dev-build    - Build and start development environment"
	@echo "  make dev-logs     - View development logs"
	@echo ""
	@echo "Production:"
	@echo "  make prod         - Start production environment"
	@echo "  make prod-build   - Build and start production environment"
	@echo ""
	@echo "Testing:"
	@echo "  make test         - Run all tests"
	@echo "  make test-backend - Run backend tests"
	@echo "  make test-frontend - Run frontend tests"
	@echo "  make test-services - Run microservices tests"
	@echo ""
	@echo "Utilities:"
	@echo "  make logs         - View all service logs"
	@echo "  make stop         - Stop all services"
	@echo "  make restart      - Restart all services"
	@echo "  make clean        - Clean up containers and volumes"
	@echo "  make health       - Check service health"
	@echo ""
	@echo "Database:"
	@echo "  make db-backup    - Backup databases"
	@echo "  make db-restore   - Restore databases"
	@echo "  make db-reset     - Reset databases (WARNING: destroys data)"

# Development commands
dev:
	@echo "🚀 Starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Development environment started!"
	@echo "Frontend: http://localhost:3000"
	@echo "API Gateway: http://localhost:8080"
	@echo "Backend API: http://localhost:8000"

dev-build:
	@echo "🔨 Building and starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d --build
	@echo "✅ Development environment built and started!"

dev-logs:
	docker-compose -f docker-compose.dev.yml logs -f

# Production commands
prod:
	@echo "🚀 Starting production environment..."
	docker-compose up -d
	@echo "✅ Production environment started!"

prod-build:
	@echo "🔨 Building and starting production environment..."
	docker-compose up -d --build
	@echo "✅ Production environment built and started!"

# Testing commands
test: test-backend test-frontend test-services

test-backend:
	@echo "🧪 Running backend tests..."
	docker-compose -f docker-compose.dev.yml exec backend python -m pytest tests/ -v

test-frontend:
	@echo "🧪 Running frontend tests..."
	docker-compose -f docker-compose.dev.yml exec frontend npm test -- --coverage --watchAll=false

test-services:
	@echo "🧪 Running microservices tests..."
	docker-compose -f docker-compose.dev.yml exec travel-search npm test
	docker-compose -f docker-compose.dev.yml exec recommendations npm test
	docker-compose -f docker-compose.dev.yml exec notifications npm test
	docker-compose -f docker-compose.dev.yml exec reviews npm test

# Utility commands
logs:
	docker-compose logs -f

stop:
	@echo "🛑 Stopping all services..."
	docker-compose down
	docker-compose -f docker-compose.dev.yml down
	@echo "✅ All services stopped!"

restart:
	@echo "🔄 Restarting all services..."
	docker-compose restart
	@echo "✅ All services restarted!"

clean:
	@echo "🧹 Cleaning up containers and volumes..."
	docker-compose down -v --remove-orphans
	docker-compose -f docker-compose.dev.yml down -v --remove-orphans
	docker system prune -f
	@echo "✅ Cleanup completed!"

health:
	@echo "🔍 Checking service health..."
	@curl -s http://localhost:8080/health | jq '.' || echo "API Gateway not responding"
	@curl -s http://localhost:8000/health | jq '.' || echo "Backend not responding"
	@curl -s http://localhost:3001/health | jq '.' || echo "Travel Search not responding"
	@curl -s http://localhost:3002/health | jq '.' || echo "Recommendations not responding"
	@curl -s http://localhost:3003/health | jq '.' || echo "Notifications not responding"
	@curl -s http://localhost:3004/health | jq '.' || echo "Reviews not responding"

# Database commands
db-backup:
	@echo "💾 Backing up databases..."
	@mkdir -p backups/$(shell date +%Y%m%d_%H%M%S)
	docker exec vtravels_mysql_dev mysqldump -u root -prootpassword vtravels > backups/$(shell date +%Y%m%d_%H%M%S)/mysql_backup.sql
	docker exec vtravels_mongodb_dev mongodump --out backups/$(shell date +%Y%m%d_%H%M%S)/mongodb
	@echo "✅ Database backup completed!"

db-restore:
	@echo "📥 Restoring databases..."
	@echo "Please specify backup directory: make db-restore BACKUP_DIR=backups/20240101_120000"
	@if [ -z "$(BACKUP_DIR)" ]; then echo "❌ BACKUP_DIR not specified"; exit 1; fi
	docker exec -i vtravels_mysql_dev mysql -u root -prootpassword vtravels < $(BACKUP_DIR)/mysql_backup.sql
	docker exec vtravels_mongodb_dev mongorestore $(BACKUP_DIR)/mongodb
	@echo "✅ Database restore completed!"

db-reset:
	@echo "⚠️  WARNING: This will destroy all data!"
	@echo "Are you sure? Type 'yes' to continue:"
	@read confirm && [ "$$confirm" = "yes" ] || exit 1
	docker-compose down -v
	docker-compose -f docker-compose.dev.yml down -v
	docker volume rm vtravels_mysql_data vtravels_mongodb_data vtravels_redis_data 2>/dev/null || true
	docker volume rm vtravels_mysql_dev_data vtravels_mongodb_dev_data vtravels_redis_dev_data 2>/dev/null || true
	@echo "✅ Databases reset completed!"

# Development helpers
install-deps:
	@echo "📦 Installing dependencies..."
	cd backend && pip install -r requirements.txt
	cd frontend && npm install
	cd microservices/travel-search && npm install
	cd microservices/recommendations && npm install
	cd microservices/notifications && npm install
	cd microservices/reviews && npm install
	cd api-gateway && npm install
	@echo "✅ Dependencies installed!"

format:
	@echo "🎨 Formatting code..."
	cd backend && black . && isort .
	cd frontend && npm run format
	@echo "✅ Code formatted!"

lint:
	@echo "🔍 Linting code..."
	cd backend && flake8 . && mypy .
	cd frontend && npm run lint
	@echo "✅ Code linted!"

# Docker helpers
docker-clean:
	@echo "🧹 Cleaning Docker resources..."
	docker system prune -af --volumes
	@echo "✅ Docker cleanup completed!"

docker-rebuild:
	@echo "🔨 Rebuilding all Docker images..."
	docker-compose build --no-cache
	docker-compose -f docker-compose.dev.yml build --no-cache
	@echo "✅ Docker images rebuilt!"

# Monitoring
monitor:
	@echo "📊 Service monitoring..."
	@echo "Press Ctrl+C to stop monitoring"
	watch -n 5 'docker-compose ps && echo "" && docker stats --no-stream'
