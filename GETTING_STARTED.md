# Getting Started with VTravels

Welcome to VTravels! This guide will help you get the application running on your local machine in just a few minutes.

## 🚀 Quick Start (5 minutes)

### Step 1: Prerequisites
Make sure you have these installed:
- **Docker Desktop** (includes Docker Compose)
- **Git**

That's it! Docker will handle everything else.

### Step 2: <PERSON>lone and Start
```bash
# Clone the repository
git clone <your-repository-url>
cd vtravels

# Start the application
./scripts/start-dev.sh
```

### Step 3: Access the Application
Once all services are running (about 30 seconds), open your browser:

- **🌐 Main Application**: http://localhost:3000
- **📚 API Documentation**: http://localhost:8000/docs
- **🔧 API Gateway**: http://localhost:8080/health

## 🎯 What You'll See

### Frontend (http://localhost:3000)
- **Home Page**: Beautiful landing page with travel features
- **Search**: Flight and hotel search functionality
- **Authentication**: Login/Register forms
- **Dashboard**: User dashboard (after login)

### API Documentation (http://localhost:8000/docs)
- **Interactive API Docs**: Test all endpoints directly
- **Authentication**: Try the login/register endpoints
- **Booking Management**: Explore booking APIs

## 🧪 Test the Application

### 1. Create a User Account
```bash
# Using curl
curl -X POST "http://localhost:8080/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "first_name": "John",
    "last_name": "Doe"
  }'
```

### 2. Search for Destinations
```bash
# Search destinations
curl "http://localhost:8080/api/search/destinations?q=paris"
```

### 3. Get Popular Destinations
```bash
# Get popular destinations
curl "http://localhost:8080/api/search/popular"
```

## 🛠️ Development Commands

### Using Make (Recommended)
```bash
make dev          # Start development environment
make logs         # View all service logs
make health       # Check service health
make stop         # Stop all services
make clean        # Clean up everything
```

### Using Docker Compose
```bash
# Start services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

## 🔍 Service Status

Check if all services are running:
```bash
# Quick health check
make health

# Or check individual services
curl http://localhost:8080/health  # API Gateway
curl http://localhost:8000/health  # Python Backend
curl http://localhost:3001/health  # Travel Search
curl http://localhost:3002/health  # Recommendations
curl http://localhost:3003/health  # Notifications
curl http://localhost:3004/health  # Reviews
```

## 🗄️ Database Access

### MySQL
```bash
# Connect to MySQL
docker exec -it vtravels_mysql_dev mysql -u vtravels_user -pvtravels_password vtravels

# View tables
SHOW TABLES;
```

### MongoDB
```bash
# Connect to MongoDB
docker exec -it vtravels_mongodb_dev mongo -u admin -padminpassword

# Switch to vtravels database
use vtravels

# View collections
show collections
```

### Redis
```bash
# Connect to Redis
docker exec -it vtravels_redis_dev redis-cli

# View keys
KEYS *
```

## 🐛 Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Check what's using the port
sudo lsof -i :3000

# Stop the process or change ports in docker-compose.dev.yml
```

**Services Not Starting**
```bash
# Check logs for errors
docker-compose -f docker-compose.dev.yml logs

# Restart specific service
docker-compose -f docker-compose.dev.yml restart backend
```

**Database Connection Issues**
```bash
# Reset databases
make db-reset

# Or restart database services
docker-compose -f docker-compose.dev.yml restart mysql mongodb redis
```

### Getting Help

1. **Check the logs**: `make logs`
2. **Verify service health**: `make health`
3. **Reset everything**: `make clean && make dev`
4. **Check documentation**: See `docs/` folder

## 📚 Next Steps

### For Developers
1. **Explore the Code**: Check out the project structure in `PROJECT_SUMMARY.md`
2. **API Documentation**: Read `docs/API_DOCUMENTATION.md`
3. **Deployment Guide**: See `docs/DEPLOYMENT_GUIDE.md`

### For Testing
1. **Run Tests**: `make test`
2. **Test Individual Services**: `make test-backend`, `make test-frontend`
3. **Load Testing**: Use the API endpoints with tools like Postman

### For Production
1. **Environment Setup**: Copy `.env.example` to `.env` and configure
2. **Production Deployment**: `docker-compose up -d`
3. **Monitoring**: Set up health checks and logging

## 🎉 You're Ready!

Congratulations! You now have a fully functional travel booking platform running locally. 

**What's included:**
- ✅ User authentication and management
- ✅ Travel search functionality
- ✅ Booking system foundation
- ✅ Review and rating system
- ✅ Real-time notifications
- ✅ Personalized recommendations
- ✅ Responsive web interface
- ✅ Complete API documentation
- ✅ Microservices architecture
- ✅ Database integration (MySQL + MongoDB + Redis)

**Ready for:**
- 🚀 Feature development
- 🧪 Testing and validation
- 📈 Scaling and optimization
- 🌍 Production deployment

Happy coding! 🎯
