version: '3.8'

services:
  # Databases
  mysql:
    image: mysql:8.0
    container_name: vtravels_mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: vtravels
      MYSQL_USER: vtravels_user
      MYSQL_PASSWORD: vtravels_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./databases/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - vtravels_network

  mongodb:
    image: mongo:6.0
    container_name: vtravels_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: adminpassword
      MONGO_INITDB_DATABASE: vtravels
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./databases/mongodb/init.js:/docker-entrypoint-initdb.d/init.js
    networks:
      - vtravels_network

  redis:
    image: redis:7.0-alpine
    container_name: vtravels_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - vtravels_network

  # Python Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: vtravels_backend
    environment:
      - DATABASE_URL=mysql+pymysql://vtravels_user:vtravels_password@mysql:3306/vtravels
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - mongodb
      - redis
    volumes:
      - ./backend:/app
    networks:
      - vtravels_network

  # Node.js Microservices
  travel-search:
    build:
      context: ./microservices/travel-search
      dockerfile: Dockerfile
    container_name: vtravels_travel_search
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3001
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/travel-search:/app
    networks:
      - vtravels_network

  recommendations:
    build:
      context: ./microservices/recommendations
      dockerfile: Dockerfile
    container_name: vtravels_recommendations
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3002
    ports:
      - "3002:3002"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/recommendations:/app
    networks:
      - vtravels_network

  notifications:
    build:
      context: ./microservices/notifications
      dockerfile: Dockerfile
    container_name: vtravels_notifications
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3003
    ports:
      - "3003:3003"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/notifications:/app
    networks:
      - vtravels_network

  reviews:
    build:
      context: ./microservices/reviews
      dockerfile: Dockerfile
    container_name: vtravels_reviews
    environment:
      - MONGODB_URL=*********************************************************************
      - REDIS_URL=redis://redis:6379
      - PORT=3004
    ports:
      - "3004:3004"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./microservices/reviews:/app
    networks:
      - vtravels_network

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: vtravels_api_gateway
    environment:
      - BACKEND_URL=http://backend:8000
      - TRAVEL_SEARCH_URL=http://travel-search:3001
      - RECOMMENDATIONS_URL=http://recommendations:3002
      - NOTIFICATIONS_URL=http://notifications:3003
      - REVIEWS_URL=http://reviews:3004
    ports:
      - "8080:8080"
    depends_on:
      - backend
      - travel-search
      - recommendations
      - notifications
      - reviews
    volumes:
      - ./api-gateway:/app
    networks:
      - vtravels_network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: vtravels_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8080
    ports:
      - "3000:3000"
    depends_on:
      - api-gateway
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - vtravels_network

volumes:
  mysql_data:
  mongodb_data:
  redis_data:

networks:
  vtravels_network:
    driver: bridge
