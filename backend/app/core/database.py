"""
Database connection and session management for VTravels Backend API
Handles MySQL (SQLAlchemy) and MongoDB (Motor) connections.
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure
import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from .config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy setup for MySQL
engine = create_engine(
    settings.mysql_url,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

Base = declarative_base()
metadata = MetaData()

# MongoDB setup
mongodb_client: Optional[AsyncIOMotorClient] = None
mongodb_database = None


async def init_db():
    """Initialize database connections."""
    global mongodb_client, mongodb_database
    
    try:
        # Test MySQL connection
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("MySQL connection established successfully")
        
        # Initialize MongoDB connection
        mongodb_client = AsyncIOMotorClient(settings.mongodb_url)
        mongodb_database = mongodb_client[settings.MONGODB_DATABASE]
        
        # Test MongoDB connection
        await mongodb_client.admin.command('ping')
        logger.info("MongoDB connection established successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        raise


async def close_db():
    """Close database connections."""
    global mongodb_client
    
    try:
        if mongodb_client:
            mongodb_client.close()
            logger.info("MongoDB connection closed")
        
        engine.dispose()
        logger.info("MySQL connection closed")
        
    except Exception as e:
        logger.error(f"Error closing database connections: {str(e)}")


def get_db() -> Session:
    """
    Dependency to get MySQL database session.
    Use this as a FastAPI dependency.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_mongodb():
    """
    Dependency to get MongoDB database instance.
    Use this as a FastAPI dependency.
    """
    if mongodb_database is None:
        raise ConnectionFailure("MongoDB not initialized")
    return mongodb_database


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[Session, None]:
    """
    Async context manager for database sessions.
    Use for manual session management.
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


class DatabaseManager:
    """Database manager class for handling both MySQL and MongoDB operations."""
    
    def __init__(self):
        self.mysql_session: Optional[Session] = None
        self.mongodb = mongodb_database
    
    def get_mysql_session(self) -> Session:
        """Get MySQL session."""
        if not self.mysql_session:
            self.mysql_session = SessionLocal()
        return self.mysql_session
    
    def close_mysql_session(self):
        """Close MySQL session."""
        if self.mysql_session:
            self.mysql_session.close()
            self.mysql_session = None
    
    async def get_mongodb_collection(self, collection_name: str):
        """Get MongoDB collection."""
        if not self.mongodb:
            raise ConnectionFailure("MongoDB not initialized")
        return self.mongodb[collection_name]
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_mysql_session()


# Database health check functions
async def check_mysql_health() -> bool:
    """Check MySQL database health."""
    try:
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"MySQL health check failed: {str(e)}")
        return False


async def check_mongodb_health() -> bool:
    """Check MongoDB database health."""
    try:
        if mongodb_client:
            await mongodb_client.admin.command('ping')
            return True
        return False
    except Exception as e:
        logger.error(f"MongoDB health check failed: {str(e)}")
        return False


async def get_database_status():
    """Get status of all database connections."""
    mysql_status = await check_mysql_health()
    mongodb_status = await check_mongodb_health()
    
    return {
        "mysql": {
            "status": "healthy" if mysql_status else "unhealthy",
            "url": settings.MYSQL_HOST
        },
        "mongodb": {
            "status": "healthy" if mongodb_status else "unhealthy",
            "url": settings.MONGODB_HOST
        }
    }
