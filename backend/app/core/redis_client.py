"""
Redis client configuration and utilities for VTravels Backend API
Handles caching, session management, and pub/sub operations.
"""

import redis.asyncio as redis
import json
import logging
from typing import Any, Optional, Union, Dict
from datetime import timedelta

from .config import settings

logger = logging.getLogger(__name__)

# Global Redis client instance
redis_client: Optional[redis.Redis] = None


async def init_redis():
    """Initialize Redis connection."""
    global redis_client
    
    try:
        redis_client = redis.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # Test connection
        await redis_client.ping()
        logger.info("Redis connection established successfully")
        
    except Exception as e:
        logger.error(f"Redis initialization failed: {str(e)}")
        raise


async def close_redis():
    """Close Redis connection."""
    global redis_client
    
    try:
        if redis_client:
            await redis_client.close()
            logger.info("Redis connection closed")
    except Exception as e:
        logger.error(f"Error closing Redis connection: {str(e)}")


async def get_redis() -> redis.Redis:
    """Get Redis client instance."""
    if redis_client is None:
        raise ConnectionError("Redis not initialized")
    return redis_client


class RedisCache:
    """Redis cache utility class."""
    
    def __init__(self, client: redis.Redis):
        self.client = client
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            value = await self.client.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {str(e)}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set value in cache with optional TTL."""
        try:
            serialized_value = json.dumps(value, default=str)
            if ttl is None:
                ttl = settings.REDIS_CACHE_TTL
            
            await self.client.set(key, serialized_value, ex=ttl)
            return True
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            result = await self.client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            result = await self.client.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {str(e)}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment numeric value in cache."""
        try:
            result = await self.client.incrby(key, amount)
            return result
        except Exception as e:
            logger.error(f"Error incrementing cache key {key}: {str(e)}")
            return None
    
    async def expire(self, key: str, ttl: Union[int, timedelta]) -> bool:
        """Set expiration time for key."""
        try:
            result = await self.client.expire(key, ttl)
            return result
        except Exception as e:
            logger.error(f"Error setting expiration for key {key}: {str(e)}")
            return False
    
    async def get_many(self, keys: list) -> Dict[str, Any]:
        """Get multiple values from cache."""
        try:
            values = await self.client.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                if value:
                    try:
                        result[key] = json.loads(value)
                    except json.JSONDecodeError:
                        result[key] = value
                else:
                    result[key] = None
            return result
        except Exception as e:
            logger.error(f"Error getting multiple cache keys: {str(e)}")
            return {}
    
    async def set_many(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set multiple values in cache."""
        try:
            pipe = self.client.pipeline()
            for key, value in mapping.items():
                serialized_value = json.dumps(value, default=str)
                pipe.set(key, serialized_value, ex=ttl or settings.REDIS_CACHE_TTL)
            
            await pipe.execute()
            return True
        except Exception as e:
            logger.error(f"Error setting multiple cache keys: {str(e)}")
            return False


class RedisSession:
    """Redis session management utility."""
    
    def __init__(self, client: redis.Redis):
        self.client = client
        self.session_prefix = "session:"
    
    async def create_session(
        self,
        session_id: str,
        user_data: Dict[str, Any],
        ttl: int = 3600
    ) -> bool:
        """Create user session."""
        try:
            key = f"{self.session_prefix}{session_id}"
            session_data = {
                "user_data": user_data,
                "created_at": str(datetime.utcnow()),
                "last_accessed": str(datetime.utcnow())
            }
            
            serialized_data = json.dumps(session_data, default=str)
            await self.client.set(key, serialized_data, ex=ttl)
            return True
        except Exception as e:
            logger.error(f"Error creating session {session_id}: {str(e)}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data."""
        try:
            key = f"{self.session_prefix}{session_id}"
            data = await self.client.get(key)
            if data:
                session_data = json.loads(data)
                # Update last accessed time
                session_data["last_accessed"] = str(datetime.utcnow())
                await self.client.set(key, json.dumps(session_data, default=str))
                return session_data
            return None
        except Exception as e:
            logger.error(f"Error getting session {session_id}: {str(e)}")
            return None
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete session."""
        try:
            key = f"{self.session_prefix}{session_id}"
            result = await self.client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {str(e)}")
            return False
    
    async def refresh_session(self, session_id: str, ttl: int = 3600) -> bool:
        """Refresh session TTL."""
        try:
            key = f"{self.session_prefix}{session_id}"
            result = await self.client.expire(key, ttl)
            return result
        except Exception as e:
            logger.error(f"Error refreshing session {session_id}: {str(e)}")
            return False


# Utility functions
async def cache_get(key: str) -> Optional[Any]:
    """Quick cache get function."""
    client = await get_redis()
    cache = RedisCache(client)
    return await cache.get(key)


async def cache_set(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """Quick cache set function."""
    client = await get_redis()
    cache = RedisCache(client)
    return await cache.set(key, value, ttl)


async def cache_delete(key: str) -> bool:
    """Quick cache delete function."""
    client = await get_redis()
    cache = RedisCache(client)
    return await cache.delete(key)


async def check_redis_health() -> bool:
    """Check Redis health."""
    try:
        if redis_client:
            await redis_client.ping()
            return True
        return False
    except Exception as e:
        logger.error(f"Redis health check failed: {str(e)}")
        return False


# Import datetime for session management
from datetime import datetime
