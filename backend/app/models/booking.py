"""
Booking models for VTravels Backend API
SQLAlchemy models for travel bookings, passengers, and related data.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Date, Enum, Text, JSON, ForeignKey, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, date
from enum import Enum as PyEnum
from typing import Optional, Dict, Any
import uuid

from ..core.database import Base


class BookingTypeEnum(PyEnum):
    FLIGHT = "flight"
    HOTEL = "hotel"
    PACKAGE = "package"
    CAR_RENTAL = "car_rental"


class BookingStatusEnum(PyEnum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"
    REFUNDED = "refunded"


class PassengerTypeEnum(PyEnum):
    ADULT = "adult"
    CHILD = "child"
    INFANT = "infant"


class GenderEnum(PyEnum):
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"


class Booking(Base):
    """Booking model for all travel bookings."""
    
    __tablename__ = "bookings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    booking_reference = Column(String(50), unique=True, nullable=False, index=True)
    booking_type = Column(Enum(BookingTypeEnum), nullable=False)
    status = Column(Enum(BookingStatusEnum), default=BookingStatusEnum.PENDING)
    total_amount = Column(DECIMAL(10, 2), nullable=False)
    currency = Column(String(3), default="USD")
    booking_data = Column(JSON, nullable=True)  # Store booking-specific data
    travel_date_start = Column(Date, nullable=False)
    travel_date_end = Column(Date, nullable=True)
    passenger_count = Column(Integer, default=1)
    special_requests = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="bookings")
    passengers = relationship("Passenger", back_populates="booking", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="booking", cascade="all, delete-orphan")
    notifications = relationship("Notification", back_populates="related_booking")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.booking_reference:
            self.booking_reference = self.generate_booking_reference()
    
    @staticmethod
    def generate_booking_reference() -> str:
        """Generate unique booking reference."""
        return f"VT{uuid.uuid4().hex[:8].upper()}"
    
    @property
    def duration_days(self) -> Optional[int]:
        """Calculate booking duration in days."""
        if self.travel_date_end:
            return (self.travel_date_end - self.travel_date_start).days
        return None
    
    @property
    def is_active(self) -> bool:
        """Check if booking is active."""
        return self.status in [BookingStatusEnum.PENDING, BookingStatusEnum.CONFIRMED]
    
    @property
    def can_be_cancelled(self) -> bool:
        """Check if booking can be cancelled."""
        return self.status in [BookingStatusEnum.PENDING, BookingStatusEnum.CONFIRMED]
    
    @property
    def is_past_travel_date(self) -> bool:
        """Check if travel date has passed."""
        return self.travel_date_start < date.today()
    
    def get_booking_details(self) -> Dict[str, Any]:
        """Get formatted booking details."""
        return {
            "booking_reference": self.booking_reference,
            "type": self.booking_type.value,
            "status": self.status.value,
            "total_amount": float(self.total_amount),
            "currency": self.currency,
            "travel_dates": {
                "start": self.travel_date_start.isoformat(),
                "end": self.travel_date_end.isoformat() if self.travel_date_end else None
            },
            "passenger_count": self.passenger_count,
            "duration_days": self.duration_days,
            "booking_data": self.booking_data,
            "special_requests": self.special_requests
        }
    
    def __repr__(self):
        return f"<Booking(id={self.id}, ref='{self.booking_reference}', type='{self.booking_type.value}', status='{self.status.value}')>"


class Passenger(Base):
    """Passenger model for booking details."""
    
    __tablename__ = "passengers"
    
    id = Column(Integer, primary_key=True, index=True)
    booking_id = Column(Integer, ForeignKey("bookings.id"), nullable=False)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    date_of_birth = Column(Date, nullable=True)
    gender = Column(Enum(GenderEnum), nullable=True)
    passport_number = Column(String(50), nullable=True)
    passport_expiry = Column(Date, nullable=True)
    nationality = Column(String(100), nullable=True)
    passenger_type = Column(Enum(PassengerTypeEnum), default=PassengerTypeEnum.ADULT)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    booking = relationship("Booking", back_populates="passengers")
    
    @property
    def full_name(self) -> str:
        """Get passenger's full name."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self) -> Optional[int]:
        """Calculate passenger's age."""
        if self.date_of_birth:
            today = date.today()
            return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
        return None
    
    @property
    def is_passport_valid(self) -> bool:
        """Check if passport is valid (not expired)."""
        if self.passport_expiry:
            return self.passport_expiry > date.today()
        return False
    
    def get_passenger_info(self) -> Dict[str, Any]:
        """Get formatted passenger information."""
        return {
            "full_name": self.full_name,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "date_of_birth": self.date_of_birth.isoformat() if self.date_of_birth else None,
            "age": self.age,
            "gender": self.gender.value if self.gender else None,
            "passenger_type": self.passenger_type.value,
            "passport": {
                "number": self.passport_number,
                "expiry": self.passport_expiry.isoformat() if self.passport_expiry else None,
                "is_valid": self.is_passport_valid
            },
            "nationality": self.nationality
        }
    
    def __repr__(self):
        return f"<Passenger(id={self.id}, booking_id={self.booking_id}, name='{self.full_name}', type='{self.passenger_type.value}')>"
