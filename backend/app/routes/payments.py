"""
Payment management routes for VTravels Backend API
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..models.user import User
from ..utils.auth import get_current_active_user

router = APIRouter()


@router.get("/")
async def get_payment_methods(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get user's payment methods."""
    return {"message": "Payment methods endpoint - to be implemented"}
