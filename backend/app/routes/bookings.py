"""
Booking management routes for VTravels Backend API
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List
from datetime import date

from ..core.database import get_db
from ..models.user import User
from ..models.booking import Booking, BookingTypeEnum, BookingStatusEnum
from ..utils.auth import get_current_active_user

router = APIRouter()


class BookingResponse(BaseModel):
    id: int
    booking_reference: str
    booking_type: str
    status: str
    total_amount: float
    currency: str
    travel_date_start: str
    travel_date_end: Optional[str]
    passenger_count: int
    created_at: str


@router.get("/", response_model=List[BookingResponse])
async def get_user_bookings(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 20
):
    """Get user's bookings."""
    bookings = db.query(Booking).filter(
        Booking.user_id == current_user.id
    ).offset(skip).limit(limit).all()
    
    return [
        BookingResponse(
            id=booking.id,
            booking_reference=booking.booking_reference,
            booking_type=booking.booking_type.value,
            status=booking.status.value,
            total_amount=float(booking.total_amount),
            currency=booking.currency,
            travel_date_start=booking.travel_date_start.isoformat(),
            travel_date_end=booking.travel_date_end.isoformat() if booking.travel_date_end else None,
            passenger_count=booking.passenger_count,
            created_at=booking.created_at.isoformat()
        )
        for booking in bookings
    ]


@router.get("/{booking_id}")
async def get_booking_details(
    booking_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get booking details."""
    booking = db.query(Booking).filter(
        Booking.id == booking_id,
        Booking.user_id == current_user.id
    ).first()
    
    if not booking:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Booking not found"
        )
    
    return booking.get_booking_details()


@router.put("/{booking_id}/cancel")
async def cancel_booking(
    booking_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Cancel a booking."""
    booking = db.query(Booking).filter(
        Booking.id == booking_id,
        Booking.user_id == current_user.id
    ).first()
    
    if not booking:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Booking not found"
        )
    
    if not booking.can_be_cancelled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Booking cannot be cancelled"
        )
    
    booking.status = BookingStatusEnum.CANCELLED
    db.commit()
    
    return {"message": "Booking cancelled successfully"}
