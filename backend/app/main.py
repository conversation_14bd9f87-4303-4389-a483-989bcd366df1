"""
VTravels FastAPI Backend Application
Main application entry point with FastAPI setup, middleware, and route registration.
"""

from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from core.config import settings
from core.database import init_db, close_db
from core.redis_client import init_redis, close_redis
from routes import auth, users, bookings, payments, notifications
from utils.exceptions import VTravelsException
from utils.logging_config import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting VTravels Backend API...")
    
    # Initialize databases
    await init_db()
    await init_redis()
    
    logger.info("Database connections established")
    logger.info("VTravels Backend API started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down VTravels Backend API...")
    await close_db()
    await close_redis()
    logger.info("VTravels Backend API shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="VTravels Backend API",
    description="Comprehensive travel booking platform backend API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)


# Global exception handler
@app.exception_handler(VTravelsException)
async def vtravels_exception_handler(request, exc: VTravelsException):
    """Handle custom VTravels exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.message,
            "error_code": exc.error_code,
            "details": exc.details
        }
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}"
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle unexpected exceptions."""
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "Internal server error",
            "error_code": "INTERNAL_ERROR"
        }
    )


# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "service": "vtravels-backend",
        "version": "1.0.0"
    }


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Welcome to VTravels Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# API version prefix
API_V1_PREFIX = "/api/v1"

# Include routers
app.include_router(
    auth.router,
    prefix=f"{API_V1_PREFIX}/auth",
    tags=["Authentication"]
)

app.include_router(
    users.router,
    prefix=f"{API_V1_PREFIX}/users",
    tags=["Users"]
)

app.include_router(
    bookings.router,
    prefix=f"{API_V1_PREFIX}/bookings",
    tags=["Bookings"]
)

app.include_router(
    payments.router,
    prefix=f"{API_V1_PREFIX}/payments",
    tags=["Payments"]
)

app.include_router(
    notifications.router,
    prefix=f"{API_V1_PREFIX}/notifications",
    tags=["Notifications"]
)


# Additional middleware for request logging
@app.middleware("http")
async def log_requests(request, call_next):
    """Log all incoming requests."""
    start_time = time.time()
    
    # Log request
    logger.info(
        f"Request: {request.method} {request.url.path}",
        extra={
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else None
        }
    )
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(
        f"Response: {response.status_code} - {process_time:.3f}s",
        extra={
            "status_code": response.status_code,
            "process_time": process_time
        }
    )
    
    return response


if __name__ == "__main__":
    import uvicorn
    import time
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
