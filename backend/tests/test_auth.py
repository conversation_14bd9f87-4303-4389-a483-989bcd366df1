"""
Test cases for authentication endpoints
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base
from app.models.user import User

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


class TestAuth:
    """Test authentication endpoints"""

    def test_register_user(self):
        """Test user registration"""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "Test",
                "last_name": "User",
                "phone": "+1234567890"
            }
        )
        assert response.status_code == 201
        data = response.json()
        assert "access_token" in data
        assert data["user"]["email"] == "<EMAIL>"

    def test_register_duplicate_email(self):
        """Test registration with duplicate email"""
        # First registration
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "Test",
                "last_name": "User"
            }
        )
        
        # Second registration with same email
        response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "Test2",
                "last_name": "User2"
            }
        )
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]

    def test_login_user(self):
        """Test user login"""
        # Register user first
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "Login",
                "last_name": "User"
            }
        )
        
        # Login
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["user"]["email"] == "<EMAIL>"

    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "wrongpassword"
            }
        )
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]

    def test_get_current_user(self):
        """Test getting current user info"""
        # Register and login
        register_response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "Current",
                "last_name": "User"
            }
        )
        token = register_response.json()["access_token"]
        
        # Get current user
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"

    def test_get_current_user_unauthorized(self):
        """Test getting current user without token"""
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 401

    def test_logout_user(self):
        """Test user logout"""
        # Register and login
        register_response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "Logout",
                "last_name": "User"
            }
        )
        token = register_response.json()["access_token"]
        
        # Logout
        response = client.post(
            "/api/v1/auth/logout",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 200
        assert "Successfully logged out" in response.json()["message"]


if __name__ == "__main__":
    pytest.main([__file__])
