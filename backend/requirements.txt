# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Database dependencies
sqlalchemy==2.0.23
pymysql==1.1.0
alembic==1.12.1
pymongo==4.6.0
motor==3.3.2

# Redis for caching and sessions
redis==5.0.1
aioredis==2.0.1

# Authentication and security
python-jose==3.3.0
bcrypt==4.1.2
cryptography==41.0.8

# Environment and configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for microservices communication
httpx==0.25.2
aiohttp==3.9.1

# Background tasks
celery==5.3.4
kombu==5.3.4

# Email services
fastapi-mail==1.4.1
jinja2==3.1.2

# File upload and storage
python-multipart==0.0.6
pillow==10.1.0
boto3==1.34.0  # For AWS S3 integration

# Validation and serialization
email-validator==2.1.0
phonenumbers==8.13.26

# Logging and monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
pytest-mock==3.12.0

# Development dependencies
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# CORS and middleware
fastapi-cors==0.0.6

# Date and time utilities
python-dateutil==2.8.2
pytz==2023.3

# Payment processing
stripe==7.8.0

# Data validation and parsing
validators==0.22.0
marshmallow==3.20.1

# API documentation
fastapi-users==12.1.2
